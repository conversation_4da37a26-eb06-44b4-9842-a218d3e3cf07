version: '3.8'

services:
  contextual-nl2sql:
    build: .
    container_name: contextual-nl2sql-system
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./outputs:/app/outputs
    stdin_open: true
    tty: true
    restart: unless-stopped
    
  # Optional: Add a Jupyter notebook service for interactive development
  jupyter:
    build: .
    container_name: contextual-nl2sql-jupyter
    command: jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token=''
    ports:
      - "8888:8888"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - .:/app
      - ./outputs:/app/outputs
    profiles:
      - jupyter
