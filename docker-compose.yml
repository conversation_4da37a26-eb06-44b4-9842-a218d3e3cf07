version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: contextual-nl2sql-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  contextual-nl2sql:
    build: .
    container_name: contextual-nl2sql-system
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./outputs:/app/outputs
    stdin_open: true
    tty: true
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy

  # Optional: Add a Jupyter notebook service for interactive development
  jupyter:
    build: .
    container_name: contextual-nl2sql-jupyter
    command: jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token=''
    ports:
      - "8888:8888"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - .:/app
      - ./outputs:/app/outputs
    depends_on:
      redis:
        condition: service_healthy
    profiles:
      - jupyter

volumes:
  redis_data:
