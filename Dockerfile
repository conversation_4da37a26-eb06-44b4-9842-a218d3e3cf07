# Use Python 3.10 as base image
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create directory for outputs
RUN mkdir -p /app/outputs

# Set default command
CMD ["python", "main.py"]

# Optional: Add labels for metadata
LABEL maintainer="Contextual NL2SQL System"
LABEL description="Contextual NL2SQL with DAG and Pandas AI"
LABEL version="1.0.0"
