"""
Smart Cache Demo - Intelligent Data Reuse

This script demonstrates the smart caching system that avoids unnecessary
SQL queries by reusing previously fetched data when possible.
"""

from main import ContextualNL2SQLSystem
from result_cache import SmartResultCache, CachedResult
import pandas as pd
from datetime import datetime


def demo_basic_cache_concept():
    """Demonstrate the basic concept of smart caching."""
    print("🧠 Smart Cache Concept Demo")
    print("=" * 35)
    
    print("\n💡 The Problem:")
    print("   User: 'Show top 50 suppliers'")
    print("   System: Executes SQL, returns 50 suppliers")
    print("   User: 'Show me top 3'")
    print("   Traditional System: Executes NEW SQL query ❌")
    print("   Smart System: Uses cached data ✅")
    
    print("\n🎯 Smart Cache Benefits:")
    print("   ✅ Faster responses (no database round-trip)")
    print("   ✅ Reduced database load")
    print("   ✅ Lower costs (fewer API calls)")
    print("   ✅ Better user experience")
    print("   ✅ Intelligent data reuse")


def demo_cache_derivation():
    """Demonstrate different types of cache derivation."""
    print("\n\n🔄 Cache Derivation Types")
    print("=" * 30)
    
    # Create sample cached data
    sample_data = pd.DataFrame({
        'supplier_id': range(1, 51),
        'supplier_name': [f'Supplier_{i}' for i in range(1, 51)],
        'revenue': [10000 + i * 1000 for i in range(50)],
        'region': ['North', 'South', 'East', 'West'] * 12 + ['North', 'South']
    })
    
    cache = SmartResultCache()
    
    # Add to cache
    cache.add_result(
        query_id="q1",
        original_query="Show top 50 suppliers by revenue",
        data=sample_data,
        metadata={"entity_type": "supplier", "sort_by": "revenue"}
    )
    
    print(f"📊 Cached data: {sample_data.shape[0]} suppliers")
    
    # Test different derivation types
    test_queries = [
        ("Show me top 10", "SUBSET - Take first 10 from cached 50"),
        ("Show me top 3", "SUBSET - Take first 3 from cached 50"),
        ("Count total suppliers", "AGGREGATION - Count cached records"),
        ("What's the average revenue?", "AGGREGATION - Calculate from cached data"),
        ("Show suppliers with revenue > 40000", "FILTER - Filter cached data")
    ]
    
    print(f"\n🧪 Testing Cache Derivation:")
    for query, expected in test_queries:
        can_derive = cache.can_derive_from_cache(query)
        status = "✅ CAN DERIVE" if can_derive else "❌ CANNOT DERIVE"
        print(f"   '{query}' → {status}")
        print(f"      Expected: {expected}")
        
        if can_derive:
            cache_key, cached_result = can_derive
            derived_data = cache.derive_result(cache_key, cached_result, query)
            print(f"      Result: {derived_data.shape[0]} rows")
        print()


def demo_real_world_scenario():
    """Demonstrate a real-world smart caching scenario."""
    print("\n\n🌍 Real-World Smart Caching Demo")
    print("=" * 40)
    
    system = ContextualNL2SQLSystem()
    
    # Load sample data
    data = system.load_sample_data()
    print(f"📊 Loaded sample data: {data.shape}")
    
    # Realistic conversation with smart caching
    conversation = [
        ("Show top 100 customers by revenue", "Initial query - will cache result"),
        ("Show me top 20", "Should use cache (subset of 100)"),
        ("Show me top 5", "Should use cache (subset of 20/100)"),
        ("How many customers total?", "Should use cache (count)"),
        ("What's the average revenue?", "Should use cache (aggregation)"),
        ("Show customers with revenue > 15000", "Should use cache (filter)"),
        ("Show product data", "Different data - needs SQL"),
        ("What trends do you see in customer data?", "Complex analysis - needs Pandas AI")
    ]
    
    print(f"\n💬 Processing {len(conversation)} queries:")
    
    cache_hits = 0
    sql_queries = 0
    pandas_ai_queries = 0
    
    previous_id = None
    for i, (query, expectation) in enumerate(conversation, 1):
        print(f"\n{i}. Query: '{query}'")
        print(f"   Expected: {expectation}")
        
        result = system.process_query(
            query,
            data=data if i == 1 else None,  # Only provide data for first query
            parent_query_id=previous_id
        )
        
        method = result.get('processing_method', 'unknown').upper()
        print(f"   Actual: {method}")
        
        # Track statistics
        if result.get('cache_used'):
            cache_hits += 1
            print(f"   ✅ Cache Hit! Derived from: '{result.get('original_cache_query', 'N/A')}'")
        elif method == 'SQL':
            sql_queries += 1
            if result.get('cached_for_future'):
                print(f"   💾 Result cached for future use")
        elif method == 'PANDAS_AI':
            pandas_ai_queries += 1
        
        # Show routing reasoning
        if result.get('routing_reasoning'):
            reasoning = result['routing_reasoning'][:80] + "..." if len(result['routing_reasoning']) > 80 else result['routing_reasoning']
            print(f"   Reasoning: {reasoning}")
        
        previous_id = result['query_id']
    
    # Show final statistics
    print(f"\n📊 Final Statistics:")
    print(f"   Total queries: {len(conversation)}")
    print(f"   Cache hits: {cache_hits}")
    print(f"   SQL queries: {sql_queries}")
    print(f"   Pandas AI queries: {pandas_ai_queries}")
    print(f"   Cache efficiency: {cache_hits}/{len(conversation)} = {cache_hits/len(conversation)*100:.1f}%")
    
    # Show cache contents
    cache_stats = system.get_cache_info()
    print(f"\n💾 Cache Contents:")
    print(f"   Cached results: {cache_stats['cache_size']}")
    for cached_query in cache_stats.get('cached_queries', []):
        print(f"   - '{cached_query['query'][:50]}...' ({cached_query['data_shape'][0]} rows)")
    
    return system


def demo_cache_intelligence():
    """Demonstrate the intelligence of the caching system."""
    print("\n\n🤖 Cache Intelligence Demo")
    print("=" * 35)
    
    cache = SmartResultCache()
    
    # Create different types of cached data
    customer_data = pd.DataFrame({
        'customer_id': range(1, 101),
        'name': [f'Customer_{i}' for i in range(1, 101)],
        'revenue': [5000 + i * 100 for i in range(100)],
        'region': ['North', 'South', 'East', 'West'] * 25
    })
    
    product_data = pd.DataFrame({
        'product_id': range(1, 51),
        'name': [f'Product_{i}' for i in range(1, 51)],
        'price': [100 + i * 10 for i in range(50)],
        'category': ['Electronics', 'Clothing', 'Books'] * 16 + ['Electronics', 'Clothing']
    })
    
    # Add to cache
    cache.add_result("customers", "Show all customers", customer_data)
    cache.add_result("products", "Show all products", product_data)
    
    print(f"📊 Cache contains:")
    print(f"   - Customer data: {customer_data.shape[0]} rows")
    print(f"   - Product data: {product_data.shape[0]} rows")
    
    # Test intelligent routing
    test_cases = [
        ("Show top 10 customers", "Should use customer cache"),
        ("Count customers", "Should use customer cache"),
        ("Show top 5 products", "Should use product cache"),
        ("Show customer trends", "Should use customer cache + Pandas AI"),
        ("Show supplier data", "Should use SQL (no cache available)"),
        ("Compare customers and products", "Should use both caches + Pandas AI")
    ]
    
    print(f"\n🧠 Testing Cache Intelligence:")
    for query, expected in test_cases:
        can_derive = cache.can_derive_from_cache(query)
        print(f"\n   Query: '{query}'")
        print(f"   Expected: {expected}")
        print(f"   Can derive: {'✅ YES' if can_derive else '❌ NO'}")
        
        if can_derive:
            cache_key, cached_result = can_derive
            print(f"   Would use: '{cached_result.original_query}' ({cached_result.data.shape[0]} rows)")


def demo_performance_comparison():
    """Demonstrate performance benefits of smart caching."""
    print("\n\n⚡ Performance Comparison Demo")
    print("=" * 40)
    
    import time
    
    # Simulate database query times
    def simulate_sql_query(query, rows):
        """Simulate SQL query execution time."""
        time.sleep(0.1)  # Simulate database latency
        return f"SQL result: {rows} rows"
    
    def simulate_cache_lookup(query, rows):
        """Simulate cache lookup time."""
        time.sleep(0.001)  # Cache is much faster
        return f"Cache result: {rows} rows"
    
    print("🏁 Performance Test:")
    print("   Scenario: User asks for top 50, then top 10, then top 3")
    
    # Without caching
    print("\n   WITHOUT Smart Caching:")
    start_time = time.time()
    result1 = simulate_sql_query("top 50", 50)
    result2 = simulate_sql_query("top 10", 10)  # New SQL query
    result3 = simulate_sql_query("top 3", 3)    # New SQL query
    without_cache_time = time.time() - start_time
    print(f"   Time: {without_cache_time:.3f} seconds")
    print(f"   Database queries: 3")
    
    # With caching
    print("\n   WITH Smart Caching:")
    start_time = time.time()
    result1 = simulate_sql_query("top 50", 50)     # Initial SQL query
    result2 = simulate_cache_lookup("top 10", 10)  # Cache hit
    result3 = simulate_cache_lookup("top 3", 3)    # Cache hit
    with_cache_time = time.time() - start_time
    print(f"   Time: {with_cache_time:.3f} seconds")
    print(f"   Database queries: 1")
    
    # Show improvement
    improvement = (without_cache_time - with_cache_time) / without_cache_time * 100
    print(f"\n   📈 Performance Improvement:")
    print(f"   Speed up: {improvement:.1f}% faster")
    print(f"   Database load: 67% reduction (1 vs 3 queries)")
    print(f"   Cost savings: ~67% fewer database operations")


def main():
    """Run all smart cache demos."""
    print("🚀 Smart Cache System - Complete Demo")
    print("=" * 50)
    
    try:
        # Basic concept
        demo_basic_cache_concept()
        
        # Cache derivation
        demo_cache_derivation()
        
        # Real-world scenario
        system = demo_real_world_scenario()
        
        # Cache intelligence
        demo_cache_intelligence()
        
        # Performance comparison
        demo_performance_comparison()
        
        print("\n\n✅ All smart cache demos completed!")
        
        print("\n🎯 Key Takeaways:")
        print("   ✅ Smart caching avoids unnecessary SQL queries")
        print("   ✅ Intelligent derivation from cached data")
        print("   ✅ Significant performance improvements")
        print("   ✅ Reduced database load and costs")
        print("   ✅ Better user experience with faster responses")
        print("   ✅ LLM-powered routing decisions")
        
        print("\n💡 Your example: 'top 50 suppliers' → 'top 3' uses cache!")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
