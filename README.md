# 🧠 Contextual NL2SQL System with DAG and Pandas AI

A sophisticated natural language to SQL system that maintains query context using a Directed Acyclic Graph (DAG) and provides intelligent data reasoning through Pandas AI integration.

[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![OpenAI GPT-4](https://img.shields.io/badge/OpenAI-GPT--4-green.svg)](https://openai.com/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)

---

## 🚀 Features

- **Contextual Query Understanding**: Maintains conversation history using a DAG structure
- **Intelligent Query Rewriting**: Uses GPT-4 to rewrite ambiguous follow-up queries
- **Dual Processing Modes**: Automatic routing between SQL and Pandas AI based on query complexity
- **Advanced Analytics**: Pandas AI integration for trend analysis, insights, and visualizations
- **Docker Support**: Fully containerized for easy deployment
- **Comprehensive Testing**: Full test suite with unit and integration tests

---

## 📦 Quick Start

### Prerequisites

- Python 3.10 or higher
- OpenAI API key
- Docker (optional)

### Installation

1. **Clone and setup:**
```bash
git clone <repository-url>
cd smart-nlp-sql
pip install -r requirements.txt
```

2. **Set your OpenAI API key:**
```bash
export OPENAI_API_KEY="your-api-key-here"
```

3. **Run the demo:**
```bash
python main.py
```

### Docker Setup

1. **Using Docker Compose (Recommended):**
```bash
cp .env.example .env
# Edit .env with your OpenAI API key
docker-compose up
```

2. **Using Docker directly:**
```bash
docker build -t contextual-nl2sql .
docker run -e OPENAI_API_KEY="your-key" contextual-nl2sql
```

---

## 🏗️ Architecture

### Core Components

#### 1. **DAG Module** (`dag.py`)
- **QueryNode**: Represents individual queries with metadata
- **QueryDAG**: Manages relationships and context between queries
- **Features**: Parent-child relationships, context retrieval, serialization

#### 2. **Query Rewriter** (`rewriter.py`)
- **Contextual Rewriting**: Uses GPT-4 to enhance ambiguous queries
- **Intent Analysis**: Extracts query components and intent
- **Smart Routing**: Determines whether to use SQL or Pandas AI

#### 3. **Pandas AI Runner** (`pandas_ai_runner.py`)
- **Advanced Analytics**: Complex data reasoning beyond SQL
- **Visualization**: Automatic chart and graph generation
- **Insights**: Automated pattern and anomaly detection

#### 4. **Main System** (`main.py`)
- **Orchestration**: Coordinates all components
- **Workflow Management**: Handles the complete query pipeline
- **Demo Interface**: Provides example usage and testing

---

## 💡 Usage Examples

### Basic Usage

```python
from main import ContextualNL2SQLSystem
import pandas as pd

# Initialize system
system = ContextualNL2SQLSystem()

# Load your data
data = pd.read_csv("your_data.csv")

# Process queries
result = system.process_query("Show top 10 customers by revenue", data=data)
print(f"Success: {result['success']}")
print(f"Method: {result['processing_method']}")
```

### Contextual Queries

```python
# First query
result1 = system.process_query("Show customers with revenue > 5000", data=data)

# Follow-up query (automatically contextualized)
result2 = system.process_query(
    "Break that by region", 
    parent_query_id=result1['query_id']
)

# The system automatically rewrites "Break that by region" to:
# "Show customers with revenue > 5000 broken down by region"
```

### Advanced Analytics

```python
# Queries that trigger Pandas AI
analytical_queries = [
    "What are the trends in customer behavior?",
    "Show me any anomalies in the sales data",
    "Create a visualization of revenue by region",
    "What insights can you provide about this dataset?"
]

for query in analytical_queries:
    result = system.process_query(query, data=data)
    # Automatically routed to Pandas AI for complex reasoning
```

---

## 🔄 Workflow

1. **User submits a query** (e.g., "Show top 10 customers")
2. **Query is stored** in a DAG node with metadata
3. **Follow-up query** like "Break that by region" triggers:
   - Upstream context lookup from DAG
   - Query rewriting using GPT-4
   - Intent analysis and routing decision
4. **Query execution** via SQL or Pandas AI engine
5. **Results returned** with context preserved for future queries

---

## 🧠 When to Use Pandas AI vs SQL

| Query Type | Processing Method | Example |
|------------|------------------|---------|
| Simple filters/aggregations | SQL | "Show top 10 customers" |
| Complex reasoning | Pandas AI | "What trends do you see?" |
| Visualizations | Pandas AI | "Create a chart showing..." |
| Anomaly detection | Pandas AI | "Find unusual patterns" |
| Statistical analysis | Pandas AI | "What correlations exist?" |

---

## 📁 Project Structure

```
smart-nlp-sql/
├── dag.py                 # DAG structure and query nodes
├── rewriter.py           # Query rewriting with GPT-4
├── pandas_ai_runner.py   # Pandas AI integration
├── main.py              # Main system orchestration
├── test_system.py       # Comprehensive test suite
├── example_usage.py     # Usage examples and demos
├── requirements.txt     # Python dependencies
├── Dockerfile          # Docker container setup
├── docker-compose.yml  # Docker Compose configuration
├── .env.example       # Environment variables template
└── README.md          # This file
```

---

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
python test_system.py

# Run specific test class
python -m unittest test_system.TestQueryDAG

# Run with verbose output
python test_system.py -v
```

---

## 📚 Examples

Explore detailed usage examples:

```bash
# Run all examples
python example_usage.py

# Examples include:
# - Basic query processing
# - Contextual query chains
# - Custom data analysis
# - DAG visualization
# - Error handling
# - Performance testing
```

---

## 🔧 Configuration

### Environment Variables

```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional
OPENAI_MODEL=gpt-4          # Default model
DEBUG=false                 # Debug mode
LOG_LEVEL=INFO             # Logging level
```

### System Configuration

```python
# Initialize with custom settings
system = ContextualNL2SQLSystem(
    api_key="your-key",
    model="gpt-4"
)

# Configure Pandas AI
pandas_ai = PandasAIRunner(
    api_key="your-key",
    model="gpt-4"
)
```

---

## 🚀 Deployment

### Production Deployment

1. **Environment Setup:**
```bash
# Production environment
export OPENAI_API_KEY="your-production-key"
export LOG_LEVEL="WARNING"
```

2. **Docker Production:**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

3. **Scaling Considerations:**
- Use connection pooling for database connections
- Implement caching for frequently accessed DAG nodes
- Consider rate limiting for OpenAI API calls

---

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Run the test suite: `python test_system.py`
5. Submit a pull request

---

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

## 🙏 Acknowledgments

- OpenAI for GPT-4 API
- Pandas AI team for the analytics framework
- Contributors and testers

---

## 📞 Support

For questions, issues, or contributions:
- Create an issue on GitHub
- Check the examples in `example_usage.py`
- Review the test cases in `test_system.py`
