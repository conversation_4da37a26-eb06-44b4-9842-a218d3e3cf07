"""
DAG Concept Explanation with Visual Examples

This module demonstrates what a DAG (Directed Acyclic Graph) is and why it's useful
for tracking query relationships in natural language conversations.
"""

from dag import QueryNode, QueryDAG
import pandas as pd
from datetime import datetime


def explain_dag_concept():
    """Explain the DAG concept with a simple example."""
    print("🧠 Understanding the DAG (Directed Acyclic Graph) Concept")
    print("=" * 60)
    
    print("\n📖 What is a DAG?")
    print("-" * 20)
    print("A DAG is like a family tree for your queries. It tracks:")
    print("• Which queries depend on previous queries")
    print("• What 'that', 'it', 'those' refer to in follow-up questions")
    print("• The logical flow of your data exploration")
    
    print("\n🔗 DIRECTED: Arrows show dependency")
    print("🚫 ACYCLIC: No circular references")
    print("📊 GRAPH: Network of connected nodes")


def demonstrate_simple_dag():
    """Demonstrate a simple DAG with a conversation."""
    print("\n\n💬 Example Conversation")
    print("=" * 30)
    
    # Create DAG
    dag = QueryDAG()
    
    # Simulate a conversation
    conversation = [
        "Show top 10 customers by revenue",
        "Break that down by region", 
        "Which region has the highest average?",
        "Show me customers from that region"
    ]
    
    print("User conversation:")
    for i, query in enumerate(conversation, 1):
        print(f"{i}. {query}")
    
    # Build the DAG
    previous_id = None
    for i, query in enumerate(conversation):
        node = QueryNode(original_query=query)
        
        # Add some realistic metadata
        if i == 0:
            node.sql_query = "SELECT customer_name, revenue FROM customers ORDER BY revenue DESC LIMIT 10"
            node.result_summary = "Retrieved top 10 customers with revenues from $15K to $25K"
        elif i == 1:
            node.sql_query = "SELECT region, SUM(revenue) FROM top_customers GROUP BY region"
            node.result_summary = "Revenue by region: North $85K, South $65K, East $45K, West $35K"
            node.groupings = ["region"]
        elif i == 2:
            node.sql_query = "SELECT region, AVG(revenue) FROM top_customers GROUP BY region ORDER BY AVG(revenue) DESC"
            node.result_summary = "North region has highest average revenue: $21.2K"
            node.aggregations = ["AVG"]
        else:
            node.sql_query = "SELECT * FROM customers WHERE region = 'North' ORDER BY revenue DESC"
            node.result_summary = "Retrieved 4 customers from North region"
            node.filters = ["region = 'North'"]
        
        node_id = dag.add_node(node, parent_id=previous_id, edge_type="follows")
        previous_id = node_id
    
    return dag


def visualize_dag_structure(dag):
    """Visualize the DAG structure."""
    print("\n\n📊 DAG Structure Visualization")
    print("=" * 35)
    
    print("\nNodes in the DAG:")
    for i, (node_id, node) in enumerate(dag.nodes.items(), 1):
        print(f"\nNode {i} (ID: {node_id[:8]}...):")
        print(f"  Query: {node.original_query}")
        print(f"  SQL: {node.sql_query[:50]}..." if node.sql_query else "  SQL: None")
        print(f"  Result: {node.result_summary}")
        if node.filters:
            print(f"  Filters: {node.filters}")
        if node.groupings:
            print(f"  Groupings: {node.groupings}")
        if node.aggregations:
            print(f"  Aggregations: {node.aggregations}")
    
    print(f"\nRelationships:")
    for parent_id, children in dag.edges.items():
        parent_query = dag.nodes[parent_id].original_query[:30] + "..."
        for child_id in children:
            child_query = dag.nodes[child_id].original_query[:30] + "..."
            edge_type = dag.get_edge_type(parent_id, child_id)
            print(f"  '{parent_query}' --({edge_type})--> '{child_query}'")


def demonstrate_context_retrieval(dag):
    """Demonstrate how context is retrieved from the DAG."""
    print("\n\n🔍 Context Retrieval Example")
    print("=" * 35)
    
    # Get the last node (most recent query)
    current_node = dag.get_current_node()
    if not current_node:
        return
    
    print(f"Current query: '{current_node.original_query}'")
    print("\nRetrieving context for query rewriting...")
    
    # Get upstream context
    context_nodes = dag.get_upstream_context(current_node.id, max_depth=2)
    
    print(f"\nFound {len(context_nodes)} context nodes:")
    for i, node in enumerate(context_nodes, 1):
        print(f"\nContext {i}:")
        print(f"  Query: {node.original_query}")
        print(f"  Result: {node.result_summary}")
        if node.filters:
            print(f"  Filters: {node.filters}")
        if node.groupings:
            print(f"  Groupings: {node.groupings}")
    
    # Show how this helps with rewriting
    print(f"\n💡 How this helps rewrite '{current_node.original_query}':")
    print("   Context tells us:")
    print("   • 'that region' refers to 'North region' (from previous query)")
    print("   • We're working with 'top 10 customers' (from first query)")
    print("   • We already know North has highest average revenue")
    print("   → Rewritten: 'Show me customers from North region (top 10 customers)'")


def demonstrate_branching_dag():
    """Demonstrate a more complex DAG with branching."""
    print("\n\n🌳 Complex DAG with Branching")
    print("=" * 35)
    
    dag = QueryDAG()
    
    # Root query
    root_node = QueryNode(original_query="Show all customer data")
    root_id = dag.add_node(root_node)
    
    # Branch 1: Revenue analysis
    rev1_node = QueryNode(original_query="Filter customers with revenue > 10K")
    rev1_id = dag.add_node(rev1_node, parent_id=root_id, edge_type="filters")
    
    rev2_node = QueryNode(original_query="Group those by region")
    rev2_id = dag.add_node(rev2_node, parent_id=rev1_id, edge_type="groups")
    
    # Branch 2: Geographic analysis
    geo1_node = QueryNode(original_query="Show customers by region")
    geo1_id = dag.add_node(geo1_node, parent_id=root_id, edge_type="groups")
    
    geo2_node = QueryNode(original_query="Which regions have most customers?")
    geo2_id = dag.add_node(geo2_node, parent_id=geo1_id, edge_type="analyzes")
    
    # Convergence: Compare both analyses
    compare_node = QueryNode(original_query="Compare revenue patterns with customer distribution")
    dag.add_node(compare_node, parent_id=rev2_id, edge_type="compares")
    dag.add_edge(geo2_id, compare_node.id, edge_type="compares")
    
    print("Complex DAG structure:")
    print("                    Root Query")
    print("                   /          \\")
    print("            Revenue Analysis   Geographic Analysis")
    print("                 |                    |")
    print("            Group by Region    Count by Region")
    print("                 \\                  /")
    print("                  \\                /")
    print("                   Compare Results")
    
    print(f"\nDAG has {len(dag)} nodes with complex relationships")
    print("This allows the system to understand:")
    print("• Multiple analysis paths from the same data")
    print("• How different analyses relate to each other")
    print("• Context for comparative questions")
    
    return dag


def demonstrate_real_world_benefits():
    """Show real-world benefits of using DAG."""
    print("\n\n🎯 Real-World Benefits")
    print("=" * 25)
    
    print("Without DAG (traditional chatbot):")
    print("❌ User: 'Show top customers'")
    print("❌ Bot: [Shows results]")
    print("❌ User: 'Break that down by region'")
    print("❌ Bot: 'What do you want to break down?' (Lost context)")
    
    print("\nWith DAG (our system):")
    print("✅ User: 'Show top customers'")
    print("✅ Bot: [Shows results, stores in DAG]")
    print("✅ User: 'Break that down by region'")
    print("✅ Bot: Looks up DAG → 'that' = 'top customers'")
    print("✅ Bot: Rewrites to 'Break down top customers by region'")
    print("✅ Bot: [Shows regional breakdown]")
    
    print("\nKey Benefits:")
    print("🧠 Context Awareness: Remembers conversation history")
    print("🔄 Query Rewriting: Converts ambiguous queries to clear ones")
    print("📊 Smart Routing: Knows when to use SQL vs advanced analytics")
    print("🔗 Relationship Tracking: Understands how queries connect")
    print("💡 Better UX: Natural conversation flow")


def main():
    """Run all DAG explanation examples."""
    explain_dag_concept()
    
    # Build and demonstrate simple DAG
    dag = demonstrate_simple_dag()
    visualize_dag_structure(dag)
    demonstrate_context_retrieval(dag)
    
    # Show complex DAG
    demonstrate_branching_dag()
    
    # Explain benefits
    demonstrate_real_world_benefits()
    
    print("\n\n🎓 Summary")
    print("=" * 15)
    print("The DAG is the 'memory' of our system that:")
    print("1. Tracks what each query refers to")
    print("2. Enables natural follow-up questions")
    print("3. Provides context for intelligent query rewriting")
    print("4. Makes conversations feel natural and intuitive")
    
    print("\n💡 Think of it as giving the AI a 'conversation memory'")
    print("   so it can understand pronouns and references like humans do!")


if __name__ == "__main__":
    main()
