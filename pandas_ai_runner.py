"""
Pandas AI runner module for intelligent data reasoning and analysis.

This module integrates Pandas AI to provide advanced data analysis capabilities
beyond traditional SQL queries, including trend analysis, insights, and visualizations.
"""

import os
import pandas as pd
from typing import Optional, Dict, Any, Union
import tempfile
import matplotlib.pyplot as plt
import seaborn as sns
from pandasai import Agent
from pandasai.llm import OpenAI


class PandasAIRunner:
    """
    Handles data analysis using Pandas AI for complex reasoning tasks.
    
    This class provides an interface to Pandas AI for answering analytical
    questions that go beyond simple SQL queries.
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4"):
        """
        Initialize the PandasAIRunner.
        
        Args:
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use for analysis
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key must be provided or set in OPENAI_API_KEY environment variable")
        
        self.model = model
        self.llm = OpenAI(api_token=self.api_key, model=model)
        self.agent = None
        self.current_df = None
        
        # Set up matplotlib for non-interactive backend
        plt.switch_backend('Agg')
        
        # Configure seaborn style
        sns.set_style("whitegrid")
    
    def analyze_data(self, df: pd.DataFrame, query: str, 
                    context: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze data using Pandas AI.
        
        Args:
            df: The DataFrame to analyze
            query: The analytical question to answer
            context: Additional context about the data or previous queries
            
        Returns:
            Dictionary containing the analysis results
        """
        try:
            # Store the current DataFrame
            self.current_df = df.copy()
            
            # Create a new agent for this analysis
            self.agent = Agent(df, config={"llm": self.llm, "verbose": True})
            
            # Build the full query with context
            full_query = self._build_analysis_query(query, context, df)
            
            # Execute the analysis
            result = self.agent.chat(full_query)
            
            # Process and format the result
            formatted_result = self._format_result(result, query)
            
            return formatted_result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "result": None,
                "query": query,
                "suggestions": self._get_error_suggestions(str(e))
            }
    
    def _build_analysis_query(self, query: str, context: Optional[str], 
                            df: pd.DataFrame) -> str:
        """
        Build a comprehensive query for Pandas AI.
        
        Args:
            query: The user's query
            context: Additional context
            df: The DataFrame being analyzed
            
        Returns:
            Enhanced query string
        """
        query_parts = []
        
        # Add data context
        query_parts.append(f"I have a dataset with {len(df)} rows and {len(df.columns)} columns.")
        query_parts.append(f"Columns: {', '.join(df.columns.tolist())}")
        
        # Add data types info
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
        
        if numeric_cols:
            query_parts.append(f"Numeric columns: {', '.join(numeric_cols)}")
        if categorical_cols:
            query_parts.append(f"Categorical columns: {', '.join(categorical_cols)}")
        
        # Add context if provided
        if context:
            query_parts.append(f"Context: {context}")
        
        # Add the main query
        query_parts.append(f"Question: {query}")
        
        return "\n".join(query_parts)
    
    def _format_result(self, result: Any, original_query: str) -> Dict[str, Any]:
        """
        Format the result from Pandas AI into a structured response.
        
        Args:
            result: Raw result from Pandas AI
            original_query: The original query
            
        Returns:
            Formatted result dictionary
        """
        formatted = {
            "success": True,
            "query": original_query,
            "result": result,
            "result_type": type(result).__name__,
            "timestamp": pd.Timestamp.now().isoformat()
        }
        
        # Handle different result types
        if isinstance(result, pd.DataFrame):
            formatted.update({
                "result_summary": f"DataFrame with {len(result)} rows and {len(result.columns)} columns",
                "columns": result.columns.tolist(),
                "sample_data": result.head().to_dict('records') if len(result) > 0 else []
            })
        elif isinstance(result, (int, float)):
            formatted.update({
                "result_summary": f"Numeric result: {result}",
                "numeric_value": result
            })
        elif isinstance(result, str):
            formatted.update({
                "result_summary": "Text analysis result",
                "text_result": result
            })
        else:
            formatted.update({
                "result_summary": f"Result of type {type(result).__name__}",
                "raw_result": str(result)
            })
        
        return formatted
    
    def _get_error_suggestions(self, error_message: str) -> list:
        """
        Provide suggestions based on error messages.
        
        Args:
            error_message: The error message
            
        Returns:
            List of suggestions
        """
        suggestions = []
        
        error_lower = error_message.lower()
        
        if "api" in error_lower or "key" in error_lower:
            suggestions.append("Check your OpenAI API key configuration")
        
        if "column" in error_lower:
            suggestions.append("Verify column names in your query")
            suggestions.append("Check if the column exists in the dataset")
        
        if "memory" in error_lower or "timeout" in error_lower:
            suggestions.append("Try with a smaller dataset or simpler query")
        
        if "plot" in error_lower or "visualization" in error_lower:
            suggestions.append("Ensure matplotlib is properly configured")
        
        if not suggestions:
            suggestions.append("Try rephrasing your question")
            suggestions.append("Check if your data has the expected structure")
        
        return suggestions
    
    def generate_insights(self, df: pd.DataFrame, 
                         focus_areas: Optional[list] = None) -> Dict[str, Any]:
        """
        Generate automatic insights about the dataset.
        
        Args:
            df: The DataFrame to analyze
            focus_areas: Specific areas to focus on (optional)
            
        Returns:
            Dictionary containing insights
        """
        try:
            self.current_df = df.copy()
            self.agent = Agent(df, config={"llm": self.llm})
            
            # Build insights query
            insights_query = self._build_insights_query(df, focus_areas)
            
            # Get insights
            insights = self.agent.chat(insights_query)
            
            return {
                "success": True,
                "insights": insights,
                "data_summary": self._get_data_summary(df),
                "timestamp": pd.Timestamp.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "insights": None
            }
    
    def _build_insights_query(self, df: pd.DataFrame, 
                            focus_areas: Optional[list] = None) -> str:
        """Build a query for generating insights."""
        query_parts = [
            f"Analyze this dataset with {len(df)} rows and {len(df.columns)} columns.",
            "Provide key insights including:"
        ]
        
        default_areas = [
            "Data distribution and patterns",
            "Notable trends or anomalies", 
            "Correlations between variables",
            "Summary statistics highlights",
            "Data quality observations"
        ]
        
        areas_to_analyze = focus_areas if focus_areas else default_areas
        
        for area in areas_to_analyze:
            query_parts.append(f"- {area}")
        
        query_parts.append("Keep insights concise and actionable.")
        
        return "\n".join(query_parts)
    
    def _get_data_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get a basic summary of the DataFrame."""
        return {
            "shape": df.shape,
            "columns": df.columns.tolist(),
            "dtypes": df.dtypes.to_dict(),
            "missing_values": df.isnull().sum().to_dict(),
            "numeric_summary": df.describe().to_dict() if len(df.select_dtypes(include=['number']).columns) > 0 else {}
        }
    
    def create_visualization(self, df: pd.DataFrame, query: str) -> Dict[str, Any]:
        """
        Create visualizations based on the query.
        
        Args:
            df: The DataFrame to visualize
            query: Description of the desired visualization
            
        Returns:
            Dictionary containing visualization info
        """
        try:
            self.current_df = df.copy()
            self.agent = Agent(df, config={"llm": self.llm})
            
            # Create visualization query
            viz_query = f"Create a visualization for: {query}. Save the plot and return the file path."
            
            # Execute visualization
            result = self.agent.chat(viz_query)
            
            return {
                "success": True,
                "visualization_created": True,
                "result": result,
                "query": query
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "visualization_created": False
            }
