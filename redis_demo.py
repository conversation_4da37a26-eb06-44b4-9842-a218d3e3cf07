"""
Redis Smart Cache Demo

This script demonstrates the Redis-based smart caching system for
distributed, persistent, and scalable query result storage.
"""

import os
import pandas as pd
from datetime import datetime
from main import ContextualNL2SQLSystem
from redis_cache import RedisSmartCache
import time


def check_redis_connection():
    """Check if Redis is available."""
    print("🔍 Checking Redis Connection")
    print("-" * 30)
    
    try:
        cache = RedisSmartCache()
        stats = cache.get_cache_stats()
        
        if stats.get('status') == 'Connected':
            print("✅ Redis is connected and ready!")
            print(f"   Redis version: {stats.get('redis_version', 'Unknown')}")
            print(f"   Memory usage: {stats.get('redis_memory_usage', 'Unknown')}")
            return True
        else:
            print(f"❌ Redis connection failed: {stats.get('status', 'Unknown')}")
            return False
            
    except Exception as e:
        print(f"❌ Redis connection error: {e}")
        print("\n💡 To start Redis:")
        print("   Option 1: docker run -d -p 6379:6379 redis:7-alpine")
        print("   Option 2: docker-compose up redis")
        print("   Option 3: Install Redis locally")
        return False


def demo_redis_basic_operations():
    """Demonstrate basic Redis cache operations."""
    print("\n\n📊 Redis Basic Operations Demo")
    print("=" * 40)
    
    cache = RedisSmartCache(default_ttl=300)  # 5 minutes TTL
    
    # Create sample data
    sample_data = pd.DataFrame({
        'customer_id': range(1, 101),
        'customer_name': [f'Customer_{i}' for i in range(1, 101)],
        'revenue': [5000 + i * 100 for i in range(100)],
        'region': ['North', 'South', 'East', 'West'] * 25
    })
    
    print(f"📝 Adding sample data to Redis cache...")
    print(f"   Data shape: {sample_data.shape}")
    
    # Add to cache
    cache.add_result(
        query_id="demo_customers",
        original_query="Show top 100 customers by revenue",
        data=sample_data,
        metadata={"entity_type": "customer", "sort_by": "revenue"}
    )
    
    # Check cache stats
    stats = cache.get_cache_stats()
    print(f"✅ Data cached successfully!")
    print(f"   Cached queries: {stats.get('cached_queries', 0)}")
    print(f"   Redis memory: {stats.get('redis_memory_usage', 'Unknown')}")
    
    # Test derivation
    print(f"\n🔍 Testing cache derivation...")
    
    test_queries = [
        "Show me top 10",
        "Show me top 5", 
        "Count total customers",
        "What's the average revenue?"
    ]
    
    for query in test_queries:
        can_derive = cache.can_derive_from_cache(query)
        if can_derive:
            cache_key, cached_result = can_derive
            derived_data = cache.derive_result(cache_key, cached_result, query)
            
            if derived_data is not None:
                print(f"   ✅ '{query}' → Derived {derived_data.shape[0]} rows from cache")
            else:
                print(f"   ❌ '{query}' → Failed to derive from cache")
        else:
            print(f"   ❌ '{query}' → Cannot derive from cache")
    
    return cache


def demo_redis_persistence():
    """Demonstrate Redis persistence across sessions."""
    print("\n\n💾 Redis Persistence Demo")
    print("=" * 30)
    
    # First session - add data
    print("Session 1: Adding data to Redis...")
    cache1 = RedisSmartCache()
    
    data1 = pd.DataFrame({
        'product_id': range(1, 51),
        'product_name': [f'Product_{i}' for i in range(1, 51)],
        'price': [100 + i * 10 for i in range(50)]
    })
    
    cache1.add_result("products", "Show all products", data1)
    stats1 = cache1.get_cache_stats()
    print(f"   Added products data: {stats1.get('cached_queries', 0)} queries cached")
    
    # Simulate new session - different cache instance
    print("\nSession 2: Accessing data from Redis...")
    cache2 = RedisSmartCache()
    
    # Check if data persists
    can_derive = cache2.can_derive_from_cache("Show top 10 products")
    if can_derive:
        cache_key, cached_result = can_derive
        derived_data = cache2.derive_result(cache_key, cached_result, "Show top 10 products")
        print(f"   ✅ Data persisted! Retrieved {derived_data.shape[0]} products from Redis")
    else:
        print(f"   ❌ Data not found in Redis")
    
    stats2 = cache2.get_cache_stats()
    print(f"   Session 2 cache stats: {stats2.get('cached_queries', 0)} queries available")


def demo_redis_performance():
    """Demonstrate Redis cache performance benefits."""
    print("\n\n⚡ Redis Performance Demo")
    print("=" * 30)
    
    cache = RedisSmartCache()
    
    # Create larger dataset
    large_data = pd.DataFrame({
        'id': range(1, 10001),
        'value': [i * 1.5 for i in range(10000)],
        'category': [f'Cat_{i%100}' for i in range(10000)]
    })
    
    print(f"📊 Testing with large dataset: {large_data.shape}")
    
    # Test cache write performance
    print("\n🔄 Cache Write Performance:")
    start_time = time.time()
    cache.add_result("large_dataset", "Show large dataset", large_data)
    write_time = time.time() - start_time
    print(f"   Write time: {write_time:.3f} seconds")
    
    # Test cache read performance
    print("\n🔄 Cache Read Performance:")
    start_time = time.time()
    can_derive = cache.can_derive_from_cache("Show top 100")
    if can_derive:
        cache_key, cached_result = can_derive
        derived_data = cache.derive_result(cache_key, cached_result, "Show top 100")
        read_time = time.time() - start_time
        print(f"   Read time: {read_time:.3f} seconds")
        print(f"   Retrieved: {derived_data.shape[0]} rows")
    else:
        print("   ❌ Could not derive from cache")
    
    # Compare with simulated SQL query time
    print("\n📈 Performance Comparison:")
    sql_simulation_time = 0.5  # Simulate 500ms SQL query
    if 'read_time' in locals():
        speedup = sql_simulation_time / read_time
        print(f"   Simulated SQL time: {sql_simulation_time:.3f} seconds")
        print(f"   Redis cache time: {read_time:.3f} seconds")
        print(f"   Speed improvement: {speedup:.1f}x faster")


def demo_redis_with_system():
    """Demonstrate Redis cache with the full NL2SQL system."""
    print("\n\n🚀 Redis + NL2SQL System Demo")
    print("=" * 40)
    
    # Initialize system with Redis
    system = ContextualNL2SQLSystem()
    
    # Load sample data
    data = system.load_sample_data()
    print(f"📊 Loaded sample data: {data.shape}")
    
    # Test conversation with Redis caching
    conversation = [
        ("Show top 50 customers by revenue", "Initial query - will cache in Redis"),
        ("Show me top 10", "Should use Redis cache"),
        ("Show me top 3", "Should use Redis cache"),
        ("Count total customers", "Should use Redis cache"),
        ("What's the average revenue?", "Should use Redis cache")
    ]
    
    print(f"\n💬 Processing conversation with Redis caching:")
    
    redis_hits = 0
    previous_id = None
    
    for i, (query, expectation) in enumerate(conversation, 1):
        print(f"\n{i}. Query: '{query}'")
        print(f"   Expected: {expectation}")
        
        result = system.process_query(
            query,
            data=data if i == 1 else None,
            parent_query_id=previous_id
        )
        
        method = result.get('processing_method', 'unknown').upper()
        print(f"   Actual: {method}")
        
        if result.get('cache_used'):
            redis_hits += 1
            print(f"   ✅ Redis Cache Hit!")
        elif result.get('cached_for_future'):
            print(f"   💾 Stored in Redis for future use")
        
        previous_id = result['query_id']
    
    # Show Redis statistics
    cache_stats = system.get_cache_info()
    print(f"\n📊 Final Redis Statistics:")
    print(f"   Status: {cache_stats.get('status', 'Unknown')}")
    print(f"   Cached queries: {cache_stats.get('cached_queries', 0)}")
    print(f"   Redis hits: {redis_hits}/{len(conversation)}")
    print(f"   Cache efficiency: {redis_hits/len(conversation)*100:.1f}%")
    print(f"   Memory usage: {cache_stats.get('redis_memory_usage', 'Unknown')}")


def demo_redis_cleanup():
    """Demonstrate Redis cache cleanup operations."""
    print("\n\n🧹 Redis Cleanup Demo")
    print("=" * 25)
    
    cache = RedisSmartCache()
    
    # Show current cache state
    stats_before = cache.get_cache_stats()
    print(f"Before cleanup:")
    print(f"   Cached queries: {stats_before.get('cached_queries', 0)}")
    print(f"   Memory usage: {stats_before.get('redis_memory_usage', 'Unknown')}")
    
    # Clean up expired entries
    print(f"\n🔄 Cleaning up expired entries...")
    cache.remove_expired()
    
    # Clear all cache
    print(f"🗑️  Clearing all cache...")
    cache.clear_cache()
    
    # Show final state
    stats_after = cache.get_cache_stats()
    print(f"\nAfter cleanup:")
    print(f"   Cached queries: {stats_after.get('cached_queries', 0)}")
    print(f"   Memory usage: {stats_after.get('redis_memory_usage', 'Unknown')}")
    
    cache.close()


def main():
    """Run all Redis cache demos."""
    print("🚀 Redis Smart Cache - Complete Demo")
    print("=" * 50)
    
    # Check Redis connection first
    if not check_redis_connection():
        print("\n❌ Redis is not available. Please start Redis and try again.")
        return
    
    try:
        # Basic operations
        cache = demo_redis_basic_operations()
        
        # Persistence
        demo_redis_persistence()
        
        # Performance
        demo_redis_performance()
        
        # Full system integration
        demo_redis_with_system()
        
        # Cleanup
        demo_redis_cleanup()
        
        print("\n✅ All Redis demos completed successfully!")
        
        print("\n🎯 Redis Cache Benefits:")
        print("   ✅ Distributed caching across multiple instances")
        print("   ✅ Persistent storage survives application restarts")
        print("   ✅ Automatic TTL management")
        print("   ✅ High performance with sub-millisecond access")
        print("   ✅ Scalable to handle large datasets")
        print("   ✅ Production-ready with clustering support")
        
        print("\n💡 Your example: 'top 50 suppliers' → 'top 3' now uses Redis!")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
