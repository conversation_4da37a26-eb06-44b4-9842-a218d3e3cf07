"""
Redis-based Smart Cache for Query Results

This module implements intelligent caching using Redis for distributed,
persistent, and scalable query result storage with smart derivation capabilities.
"""

import redis
import pandas as pd
import pickle
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
import os


@dataclass
class RedisCachedResult:
    """
    Represents a cached query result stored in Redis.
    """
    query_id: str
    original_query: str
    data_shape: Tuple[int, int]  # Store shape instead of full data
    timestamp: str
    metadata: Dict[str, Any]
    ttl_seconds: int = 3600  # 1 hour default TTL
    
    def can_derive(self, new_query: str, new_metadata: Dict[str, Any]) -> bool:
        """
        Check if a new query can be derived from this cached result.
        """
        return self._can_subset(new_query, new_metadata) or \
               self._can_aggregate(new_query, new_metadata) or \
               self._can_filter(new_query, new_metadata)
    
    def _can_subset(self, new_query: str, new_metadata: Dict[str, Any]) -> bool:
        """Check if new query is a subset of cached data."""
        new_query_lower = new_query.lower()
        original_lower = self.original_query.lower()
        
        # Pattern: "top N" where N < original N
        if "top" in new_query_lower and "top" in original_lower:
            try:
                new_num = self._extract_number(new_query)
                original_num = self._extract_number(self.original_query)
                
                if new_num and original_num and new_num <= original_num:
                    if self._same_entity_type(new_query, self.original_query):
                        return True
            except:
                pass
        
        return False
    
    def _can_aggregate(self, new_query: str, new_metadata: Dict[str, Any]) -> bool:
        """Check if new query is an aggregation of cached data."""
        new_query_lower = new_query.lower()
        agg_keywords = ["sum", "average", "count", "total", "mean", "max", "min"]
        return any(keyword in new_query_lower for keyword in agg_keywords)
    
    def _can_filter(self, new_query: str, new_metadata: Dict[str, Any]) -> bool:
        """Check if new query is a filter of cached data."""
        new_query_lower = new_query.lower()
        filter_keywords = ["where", "filter", "only", "with", "having"]
        return any(keyword in new_query_lower for keyword in filter_keywords)
    
    def _extract_number(self, query: str) -> Optional[int]:
        """Extract number from query like 'top 50' or 'first 10'."""
        import re
        numbers = re.findall(r'\b(\d+)\b', query)
        return int(numbers[0]) if numbers else None
    
    def _same_entity_type(self, query1: str, query2: str) -> bool:
        """Check if two queries refer to the same entity type."""
        entities = ["customer", "supplier", "product", "order", "sale", "employee"]
        query1_entities = [e for e in entities if e in query1.lower()]
        query2_entities = [e for e in entities if e in query2.lower()]
        return len(set(query1_entities) & set(query2_entities)) > 0


class RedisSmartCache:
    """
    Redis-based intelligent cache for query results with smart derivation.
    
    Features:
    - Distributed caching with Redis
    - Automatic TTL management
    - Intelligent query derivation
    - Compression for large DataFrames
    - Connection pooling and failover
    """
    
    def __init__(self, 
                 redis_host: str = "localhost",
                 redis_port: int = 6379,
                 redis_db: int = 0,
                 redis_password: Optional[str] = None,
                 default_ttl: int = 3600,
                 key_prefix: str = "nlp2sql:cache:"):
        """
        Initialize Redis cache.
        
        Args:
            redis_host: Redis server host
            redis_port: Redis server port
            redis_db: Redis database number
            redis_password: Redis password (if required)
            default_ttl: Default TTL in seconds
            key_prefix: Prefix for all cache keys
        """
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix
        self.logger = logging.getLogger(__name__)
        
        # Redis connection configuration
        self.redis_config = {
            'host': redis_host,
            'port': redis_port,
            'db': redis_db,
            'password': redis_password,
            'decode_responses': False,  # We'll handle binary data
            'socket_connect_timeout': 5,
            'socket_timeout': 5,
            'retry_on_timeout': True,
            'health_check_interval': 30
        }
        
        # Initialize Redis connection
        self.redis_client = None
        self._connect()
    
    def _connect(self):
        """Establish Redis connection with error handling."""
        try:
            self.redis_client = redis.Redis(**self.redis_config)
            # Test connection
            self.redis_client.ping()
            self.logger.info("Connected to Redis successfully")
        except redis.ConnectionError as e:
            self.logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
        except Exception as e:
            self.logger.error(f"Unexpected error connecting to Redis: {e}")
            self.redis_client = None
    
    def _is_connected(self) -> bool:
        """Check if Redis connection is active."""
        if not self.redis_client:
            return False
        try:
            self.redis_client.ping()
            return True
        except:
            return False
    
    def _ensure_connection(self):
        """Ensure Redis connection is active, reconnect if needed."""
        if not self._is_connected():
            self.logger.warning("Redis connection lost, attempting to reconnect...")
            self._connect()
    
    def _generate_key(self, query_id: str, suffix: str = "") -> str:
        """Generate Redis key with prefix."""
        key = f"{self.key_prefix}{query_id}"
        if suffix:
            key += f":{suffix}"
        return key
    
    def _serialize_dataframe(self, df: pd.DataFrame) -> bytes:
        """Serialize DataFrame to bytes with compression."""
        try:
            # Use pickle for efficient serialization
            return pickle.dumps(df, protocol=pickle.HIGHEST_PROTOCOL)
        except Exception as e:
            self.logger.error(f"Failed to serialize DataFrame: {e}")
            raise
    
    def _deserialize_dataframe(self, data: bytes) -> pd.DataFrame:
        """Deserialize bytes back to DataFrame."""
        try:
            return pickle.loads(data)
        except Exception as e:
            self.logger.error(f"Failed to deserialize DataFrame: {e}")
            raise
    
    def add_result(self, query_id: str, original_query: str, 
                   data: pd.DataFrame, metadata: Dict[str, Any] = None,
                   ttl_seconds: Optional[int] = None):
        """
        Add a query result to Redis cache.
        
        Args:
            query_id: Unique identifier for the query
            original_query: The original query text
            data: The result DataFrame
            metadata: Additional metadata about the query
            ttl_seconds: Time to live in seconds (uses default if None)
        """
        self._ensure_connection()
        
        if not self._is_connected():
            self.logger.warning("Redis not available, skipping cache add")
            return
        
        if metadata is None:
            metadata = {}
        
        ttl = ttl_seconds or self.default_ttl
        
        try:
            # Create cached result metadata
            cached_result = RedisCachedResult(
                query_id=query_id,
                original_query=original_query,
                data_shape=data.shape,
                timestamp=datetime.now().isoformat(),
                metadata=metadata,
                ttl_seconds=ttl
            )
            
            # Store metadata
            metadata_key = self._generate_key(query_id, "meta")
            metadata_json = json.dumps(asdict(cached_result))
            self.redis_client.setex(metadata_key, ttl, metadata_json)
            
            # Store DataFrame data
            data_key = self._generate_key(query_id, "data")
            serialized_data = self._serialize_dataframe(data)
            self.redis_client.setex(data_key, ttl, serialized_data)
            
            # Add to query index for efficient lookup
            index_key = self._generate_key("index")
            self.redis_client.sadd(index_key, query_id)
            self.redis_client.expire(index_key, ttl * 2)  # Index lives longer
            
            self.logger.info(f"Cached query result: {query_id} ({data.shape[0]} rows)")
            
        except Exception as e:
            self.logger.error(f"Failed to cache result: {e}")
    
    def can_derive_from_cache(self, new_query: str, 
                             new_metadata: Dict[str, Any] = None) -> Optional[Tuple[str, RedisCachedResult]]:
        """
        Check if a new query can be derived from cached results.
        
        Args:
            new_query: The new query to check
            new_metadata: Metadata about the new query
            
        Returns:
            Tuple of (cache_key, cached_result) if derivable, None otherwise
        """
        self._ensure_connection()
        
        if not self._is_connected():
            return None
        
        if new_metadata is None:
            new_metadata = {}
        
        try:
            # Get all cached query IDs
            index_key = self._generate_key("index")
            cached_query_ids = self.redis_client.smembers(index_key)
            
            if not cached_query_ids:
                return None
            
            # Check each cached result (most recent first)
            for query_id_bytes in cached_query_ids:
                query_id = query_id_bytes.decode('utf-8')
                
                # Get metadata
                metadata_key = self._generate_key(query_id, "meta")
                metadata_json = self.redis_client.get(metadata_key)
                
                if not metadata_json:
                    continue  # Expired or missing
                
                try:
                    metadata_dict = json.loads(metadata_json.decode('utf-8'))
                    cached_result = RedisCachedResult(**metadata_dict)
                    
                    if cached_result.can_derive(new_query, new_metadata):
                        return query_id, cached_result
                        
                except Exception as e:
                    self.logger.warning(f"Failed to parse cached metadata for {query_id}: {e}")
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to check cache derivation: {e}")
            return None
    
    def derive_result(self, cache_key: str, cached_result: RedisCachedResult, 
                     new_query: str, new_metadata: Dict[str, Any] = None) -> Optional[pd.DataFrame]:
        """
        Derive a new result from cached data in Redis.
        
        Args:
            cache_key: The cache key of the source result
            cached_result: The cached result metadata
            new_query: The new query
            new_metadata: Metadata about the new query
            
        Returns:
            Derived DataFrame result or None if failed
        """
        self._ensure_connection()
        
        if not self._is_connected():
            return None
        
        if new_metadata is None:
            new_metadata = {}
        
        try:
            # Get cached data
            data_key = self._generate_key(cache_key, "data")
            serialized_data = self.redis_client.get(data_key)
            
            if not serialized_data:
                self.logger.warning(f"Cached data not found for key: {cache_key}")
                return None
            
            # Deserialize DataFrame
            cached_data = self._deserialize_dataframe(serialized_data)
            
            # Derive based on query type
            if self._is_subset_query(new_query, cached_result.original_query):
                return self._derive_subset(cached_data, new_query)
            elif self._is_aggregation_query(new_query):
                return self._derive_aggregation(cached_data, new_query, new_metadata)
            elif self._is_filter_query(new_query):
                return self._derive_filter(cached_data, new_query, new_metadata)
            else:
                return cached_data.copy()
                
        except Exception as e:
            self.logger.error(f"Failed to derive result from cache: {e}")
            return None
    
    def _is_subset_query(self, new_query: str, original_query: str) -> bool:
        """Check if new query is asking for a subset."""
        new_query_lower = new_query.lower()
        original_lower = original_query.lower()
        return ("top" in new_query_lower and "top" in original_lower) or \
               ("first" in new_query_lower) or ("limit" in new_query_lower)
    
    def _is_aggregation_query(self, query: str) -> bool:
        """Check if query is asking for aggregation."""
        query_lower = query.lower()
        agg_keywords = ["sum", "average", "count", "total", "mean", "max", "min", "avg"]
        return any(keyword in query_lower for keyword in agg_keywords)
    
    def _is_filter_query(self, query: str) -> bool:
        """Check if query is asking for filtering."""
        query_lower = query.lower()
        filter_keywords = ["where", "filter", "only", "with", "having", "exclude"]
        return any(keyword in query_lower for keyword in filter_keywords)
    
    def _derive_subset(self, data: pd.DataFrame, new_query: str) -> pd.DataFrame:
        """Derive a subset from cached data."""
        import re
        numbers = re.findall(r'\b(\d+)\b', new_query)
        if numbers:
            limit = int(numbers[0])
            return data.head(limit).copy()
        return data.copy()
    
    def _derive_aggregation(self, data: pd.DataFrame, new_query: str, 
                           metadata: Dict[str, Any]) -> pd.DataFrame:
        """Derive aggregation from cached data."""
        query_lower = new_query.lower()
        
        if "count" in query_lower:
            return pd.DataFrame({"count": [len(data)]})
        elif "sum" in query_lower and "revenue" in query_lower:
            if "revenue" in data.columns:
                return pd.DataFrame({"total_revenue": [data["revenue"].sum()]})
        elif "average" in query_lower or "avg" in query_lower:
            numeric_cols = data.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                return pd.DataFrame({f"avg_{col}": [data[col].mean()] for col in numeric_cols})
        
        return data.describe()
    
    def _derive_filter(self, data: pd.DataFrame, new_query: str, 
                      metadata: Dict[str, Any]) -> pd.DataFrame:
        """Derive filtered data from cached data."""
        query_lower = new_query.lower()
        filtered_data = data.copy()
        
        if "high" in query_lower and "revenue" in query_lower:
            if "revenue" in data.columns:
                threshold = data["revenue"].quantile(0.8)
                filtered_data = data[data["revenue"] >= threshold]
        
        return filtered_data
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics."""
        self._ensure_connection()
        
        if not self._is_connected():
            return {"status": "Redis not available"}
        
        try:
            # Get basic Redis info
            info = self.redis_client.info()
            
            # Get cached queries count
            index_key = self._generate_key("index")
            cached_count = self.redis_client.scard(index_key)
            
            # Get memory usage
            memory_usage = info.get('used_memory_human', 'Unknown')
            
            return {
                "status": "Connected",
                "cached_queries": cached_count,
                "redis_memory_usage": memory_usage,
                "redis_version": info.get('redis_version', 'Unknown'),
                "connected_clients": info.get('connected_clients', 0),
                "total_commands_processed": info.get('total_commands_processed', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get cache stats: {e}")
            return {"status": f"Error: {e}"}
    
    def clear_cache(self):
        """Clear all cached results."""
        self._ensure_connection()
        
        if not self._is_connected():
            self.logger.warning("Redis not available, cannot clear cache")
            return
        
        try:
            # Get all keys with our prefix
            pattern = f"{self.key_prefix}*"
            keys = self.redis_client.keys(pattern)
            
            if keys:
                self.redis_client.delete(*keys)
                self.logger.info(f"Cleared {len(keys)} cache entries")
            else:
                self.logger.info("No cache entries to clear")
                
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {e}")
    
    def remove_expired(self):
        """Remove expired entries (Redis handles this automatically with TTL)."""
        # Redis automatically removes expired keys, but we can clean up the index
        self._ensure_connection()
        
        if not self._is_connected():
            return
        
        try:
            index_key = self._generate_key("index")
            cached_query_ids = self.redis_client.smembers(index_key)
            
            expired_ids = []
            for query_id_bytes in cached_query_ids:
                query_id = query_id_bytes.decode('utf-8')
                metadata_key = self._generate_key(query_id, "meta")
                
                if not self.redis_client.exists(metadata_key):
                    expired_ids.append(query_id_bytes)
            
            if expired_ids:
                self.redis_client.srem(index_key, *expired_ids)
                self.logger.info(f"Cleaned up {len(expired_ids)} expired entries from index")
                
        except Exception as e:
            self.logger.error(f"Failed to remove expired entries: {e}")
    
    def close(self):
        """Close Redis connection."""
        if self.redis_client:
            try:
                self.redis_client.close()
                self.logger.info("Redis connection closed")
            except Exception as e:
                self.logger.error(f"Error closing Redis connection: {e}")
            finally:
                self.redis_client = None
