"""
Main application for the Contextual NL2SQL System with DAG and Pandas AI.

This module orchestrates the entire workflow, demonstrating how the components
work together to provide intelligent data querying and analysis.
"""

import os
import pandas as pd
from datetime import datetime
from typing import Optional, Dict, Any, List

from dag import QueryNode, QueryDAG
from rewriter import QueryRewriter
from pandas_ai_runner import PandasAIRunner


class ContextualNL2SQLSystem:
    """
    Main system class that orchestrates the contextual NL2SQL workflow.
    
    This class integrates the DAG, query rewriter, and Pandas AI components
    to provide a complete natural language data analysis system.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the system.
        
        Args:
            api_key: OpenAI API key (if None, will try to get from environment)
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            print("Warning: No OpenAI API key found. Some features will be limited.")
        
        self.dag = QueryDAG()
        self.rewriter = QueryRewriter(api_key=self.api_key) if self.api_key else None
        self.pandas_ai = PandasAIRunner(api_key=self.api_key) if self.api_key else None
        self.current_data = None
    
    def process_query(self, query: str, data: Optional[pd.DataFrame] = None,
                     parent_query_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a natural language query through the complete pipeline.
        
        Args:
            query: The natural language query
            data: Optional DataFrame to analyze
            parent_query_id: ID of parent query for context
            
        Returns:
            Dictionary containing the complete response
        """
        # Create a new query node
        node = QueryNode(original_query=query)
        
        # Add to DAG
        node_id = self.dag.add_node(node, parent_id=parent_query_id)
        
        # Store data if provided
        if data is not None:
            self.current_data = data
        
        response = {
            "query_id": node_id,
            "original_query": query,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        try:
            # Step 1: Rewrite query if needed and rewriter is available
            rewritten_query = query
            if self.rewriter and self.dag.get_upstream_context(node_id):
                rewritten_query = self.rewriter.rewrite_query(query, self.dag, node_id)
                node.rewritten_query = rewritten_query
                response["rewritten_query"] = rewritten_query
            
            # Step 2: Determine processing approach
            use_pandas_ai = False
            if self.rewriter:
                use_pandas_ai = self.rewriter.should_use_pandas_ai(
                    rewritten_query, 
                    sql_result_available=(self.current_data is not None)
                )
            
            response["processing_method"] = "pandas_ai" if use_pandas_ai else "sql"
            
            # Step 3: Process the query
            if use_pandas_ai and self.pandas_ai and self.current_data is not None:
                # Use Pandas AI for complex analysis
                context = self._build_context_for_pandas_ai(node_id)
                analysis_result = self.pandas_ai.analyze_data(
                    self.current_data, 
                    rewritten_query, 
                    context
                )
                response.update(analysis_result)
                
                # Update node with results
                if analysis_result.get("success"):
                    node.result_summary = str(analysis_result.get("result", ""))[:200]
                
            else:
                # Simulate SQL processing (placeholder)
                sql_result = self._simulate_sql_processing(rewritten_query)
                response.update(sql_result)
                
                # Update node with SQL info
                node.sql_query = sql_result.get("sql_query")
                node.result_summary = sql_result.get("result_summary")
            
            # Step 4: Extract metadata from query
            if self.rewriter:
                intent_analysis = self.rewriter.analyze_query_intent(rewritten_query)
                node.filters = intent_analysis.get("filters", [])
                node.groupings = intent_analysis.get("groupings", [])
                node.aggregations = intent_analysis.get("aggregations", [])
                response["intent_analysis"] = intent_analysis
            
            return response
            
        except Exception as e:
            response.update({
                "success": False,
                "error": str(e),
                "suggestions": ["Check your API key", "Verify data format", "Try a simpler query"]
            })
            return response
    
    def _build_context_for_pandas_ai(self, node_id: str) -> str:
        """Build context string for Pandas AI from DAG history."""
        context_nodes = self.dag.get_upstream_context(node_id, max_depth=2)
        
        if not context_nodes:
            return ""
        
        context_parts = ["Previous analysis context:"]
        for i, node in enumerate(context_nodes, 1):
            context_parts.append(f"{i}. {node.original_query}")
            if node.result_summary:
                context_parts.append(f"   Result: {node.result_summary}")
        
        return "\n".join(context_parts)
    
    def _simulate_sql_processing(self, query: str) -> Dict[str, Any]:
        """
        Simulate SQL query processing (placeholder implementation).
        
        In a real implementation, this would:
        1. Convert NL to SQL using an NL2SQL model
        2. Execute the SQL against a database
        3. Return the results
        """
        # Simple simulation
        sql_query = f"-- Generated from: {query}\nSELECT * FROM data_table WHERE condition;"
        
        # Simulate result based on current data
        if self.current_data is not None:
            result_summary = f"Query returned {len(self.current_data)} rows with columns: {', '.join(self.current_data.columns[:3])}..."
            sample_data = self.current_data.head(3).to_dict('records')
        else:
            result_summary = "No data available for SQL execution"
            sample_data = []
        
        return {
            "sql_query": sql_query,
            "result_summary": result_summary,
            "sample_data": sample_data,
            "execution_time": "0.05s"
        }
    
    def get_query_history(self) -> List[Dict[str, Any]]:
        """Get the complete query history from the DAG."""
        history = []
        for node_id, node in self.dag.nodes.items():
            history.append({
                "id": node.id,
                "original_query": node.original_query,
                "rewritten_query": node.rewritten_query,
                "sql_query": node.sql_query,
                "result_summary": node.result_summary,
                "timestamp": node.timestamp.isoformat(),
                "filters": node.filters,
                "groupings": node.groupings,
                "aggregations": node.aggregations
            })
        
        # Sort by timestamp
        history.sort(key=lambda x: x["timestamp"])
        return history
    
    def visualize_dag(self) -> Dict[str, Any]:
        """Get DAG structure for visualization."""
        return self.dag.to_dict()
    
    def load_sample_data(self) -> pd.DataFrame:
        """Load sample data for demonstration."""
        # Create sample sales data
        data = {
            'customer_id': range(1, 101),
            'customer_name': [f'Customer_{i}' for i in range(1, 101)],
            'region': ['North', 'South', 'East', 'West'] * 25,
            'revenue': [1000 + i * 100 + (i % 10) * 50 for i in range(100)],
            'order_count': [5 + (i % 15) for i in range(100)],
            'signup_date': pd.date_range('2023-01-01', periods=100, freq='D')
        }
        
        df = pd.DataFrame(data)
        self.current_data = df
        return df


def demo_workflow():
    """Demonstrate the complete workflow with sample queries."""
    print("🚀 Contextual NL2SQL System Demo")
    print("=" * 50)
    
    # Initialize system
    system = ContextualNL2SQLSystem()
    
    # Load sample data
    print("\n📊 Loading sample data...")
    data = system.load_sample_data()
    print(f"Loaded data with {len(data)} rows and {len(data.columns)} columns")
    print(f"Columns: {', '.join(data.columns)}")
    
    # Demo queries
    queries = [
        "Show top 10 customers by revenue",
        "Break that by region",
        "What are the trends in the North region?",
        "Show me any anomalies in the data"
    ]
    
    print("\n🔍 Processing demo queries...")
    
    previous_query_id = None
    for i, query in enumerate(queries, 1):
        print(f"\n--- Query {i}: {query} ---")
        
        result = system.process_query(
            query, 
            data=data if i == 1 else None,  # Only pass data for first query
            parent_query_id=previous_query_id
        )
        
        print(f"Query ID: {result['query_id']}")
        print(f"Success: {result['success']}")
        
        if result.get('rewritten_query'):
            print(f"Rewritten: {result['rewritten_query']}")
        
        print(f"Method: {result.get('processing_method', 'unknown')}")
        
        if result.get('sql_query'):
            print(f"SQL: {result['sql_query'][:100]}...")
        
        if result.get('result_summary'):
            print(f"Result: {result['result_summary']}")
        
        previous_query_id = result['query_id']
    
    # Show query history
    print("\n📈 Query History:")
    history = system.get_query_history()
    for entry in history:
        print(f"- {entry['original_query']} (ID: {entry['id'][:8]}...)")
    
    print(f"\n✅ Demo completed! Processed {len(history)} queries.")
    
    return system


if __name__ == "__main__":
    # Run the demo
    demo_system = demo_workflow()
    
    # Additional information
    print("\n💡 To use this system:")
    print("1. Set your OPENAI_API_KEY environment variable")
    print("2. Install required packages: pip install -r requirements.txt")
    print("3. Import and use the ContextualNL2SQLSystem class")
    print("4. Process queries with: system.process_query('your query here')")
