"""
Example usage of the Contextual NL2SQL System.

This script demonstrates various use cases and features of the system.
"""

import pandas as pd
import os
from main import ContextualNL2SQLSystem


def example_basic_usage():
    """Demonstrate basic system usage."""
    print("🔹 Basic Usage Example")
    print("-" * 30)
    
    # Initialize system
    system = ContextualNL2SQLSystem()
    
    # Load sample data
    data = system.load_sample_data()
    print(f"Loaded sample data: {data.shape[0]} rows, {data.shape[1]} columns")
    
    # Process a simple query
    result = system.process_query("Show top 10 customers by revenue", data=data)
    
    print(f"Query processed successfully: {result['success']}")
    print(f"Query ID: {result['query_id']}")
    print(f"Processing method: {result.get('processing_method', 'N/A')}")
    
    return system, result


def example_contextual_queries():
    """Demonstrate contextual query processing."""
    print("\n🔹 Contextual Queries Example")
    print("-" * 35)
    
    system = ContextualNL2SQLSystem()
    data = system.load_sample_data()
    
    # Sequence of related queries
    queries = [
        "Show customers with revenue over 5000",
        "Break that down by region",
        "Which region has the highest average?",
        "Show me the trend over time for that region"
    ]
    
    previous_id = None
    for i, query in enumerate(queries, 1):
        print(f"\nQuery {i}: {query}")
        
        result = system.process_query(
            query,
            data=data if i == 1 else None,  # Only pass data for first query
            parent_query_id=previous_id
        )
        
        if result.get('rewritten_query') and result['rewritten_query'] != query:
            print(f"Rewritten: {result['rewritten_query']}")
        
        print(f"Method: {result.get('processing_method', 'N/A')}")
        previous_id = result['query_id']
    
    # Show the complete query history
    print(f"\n📈 Complete Query History ({len(system.get_query_history())} queries):")
    for entry in system.get_query_history():
        print(f"- {entry['original_query']}")
    
    return system


def example_custom_data():
    """Demonstrate usage with custom data."""
    print("\n🔹 Custom Data Example")
    print("-" * 25)
    
    # Create custom dataset
    custom_data = pd.DataFrame({
        'product_id': range(1, 51),
        'product_name': [f'Product_{i}' for i in range(1, 51)],
        'category': ['Electronics', 'Clothing', 'Books', 'Home'] * 12 + ['Electronics', 'Clothing'],
        'price': [10 + i * 5 + (i % 7) * 3 for i in range(50)],
        'sales_count': [100 + (i % 20) * 10 for i in range(50)],
        'rating': [3.0 + (i % 3) + (i % 7) * 0.1 for i in range(50)]
    })
    
    print(f"Created custom dataset: {custom_data.shape}")
    print(f"Categories: {custom_data['category'].unique()}")
    
    system = ContextualNL2SQLSystem()
    
    # Analyze the custom data
    queries = [
        "What are the top selling products?",
        "Which category has the highest average rating?",
        "Show me products with unusual pricing patterns"
    ]
    
    for query in queries:
        print(f"\nQuery: {query}")
        result = system.process_query(query, data=custom_data)
        print(f"Success: {result['success']}")
        if result.get('result_summary'):
            print(f"Result: {result['result_summary']}")
    
    return system, custom_data


def example_dag_visualization():
    """Demonstrate DAG structure visualization."""
    print("\n🔹 DAG Visualization Example")
    print("-" * 30)
    
    system = ContextualNL2SQLSystem()
    data = system.load_sample_data()
    
    # Create a complex query structure
    # Root query
    root_result = system.process_query("Show all customers", data=data)
    root_id = root_result['query_id']
    
    # Branch 1: Revenue analysis
    rev1_result = system.process_query("Filter by high revenue customers", parent_query_id=root_id)
    rev2_result = system.process_query("Break down by region", parent_query_id=rev1_result['query_id'])
    
    # Branch 2: Geographic analysis  
    geo1_result = system.process_query("Group by region", parent_query_id=root_id)
    geo2_result = system.process_query("Show regional trends", parent_query_id=geo1_result['query_id'])
    
    # Get DAG structure
    dag_structure = system.visualize_dag()
    
    print(f"DAG contains {len(dag_structure['nodes'])} nodes")
    print(f"Edges: {len(dag_structure['edges'])} parent-child relationships")
    
    # Show the structure
    print("\nQuery Structure:")
    for node_id, node_data in dag_structure['nodes'].items():
        print(f"- {node_data['original_query']} (ID: {node_id[:8]}...)")
    
    print("\nRelationships:")
    for parent_child, edge_type in dag_structure['edge_types'].items():
        print(f"- {parent_child} ({edge_type})")
    
    return system


def example_error_handling():
    """Demonstrate error handling and edge cases."""
    print("\n🔹 Error Handling Example")
    print("-" * 27)
    
    system = ContextualNL2SQLSystem()
    
    # Test with no data
    result1 = system.process_query("Analyze the data")
    print(f"Query without data - Success: {result1['success']}")
    
    # Test with empty query
    result2 = system.process_query("", data=pd.DataFrame())
    print(f"Empty query - Success: {result2['success']}")
    
    # Test with malformed data
    bad_data = pd.DataFrame({'col1': [1, 2, None, 'invalid']})
    result3 = system.process_query("Summarize this data", data=bad_data)
    print(f"Malformed data - Success: {result3['success']}")
    
    return system


def example_performance_test():
    """Demonstrate system performance with larger datasets."""
    print("\n🔹 Performance Test Example")
    print("-" * 28)
    
    # Create larger dataset
    import numpy as np
    
    large_data = pd.DataFrame({
        'id': range(10000),
        'value': np.random.normal(100, 20, 10000),
        'category': np.random.choice(['A', 'B', 'C', 'D'], 10000),
        'date': pd.date_range('2023-01-01', periods=10000, freq='H')
    })
    
    print(f"Created large dataset: {large_data.shape}")
    
    system = ContextualNL2SQLSystem()
    
    # Time the query processing
    import time
    
    start_time = time.time()
    result = system.process_query("Analyze patterns in this large dataset", data=large_data)
    end_time = time.time()
    
    print(f"Query processed in {end_time - start_time:.2f} seconds")
    print(f"Success: {result['success']}")
    
    return system


def main():
    """Run all examples."""
    print("🚀 Contextual NL2SQL System - Usage Examples")
    print("=" * 55)
    
    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("⚠️  Warning: OPENAI_API_KEY not set. Some features will be limited.")
        print("   Set your API key: export OPENAI_API_KEY='your-key-here'")
    
    try:
        # Run examples
        example_basic_usage()
        example_contextual_queries()
        example_custom_data()
        example_dag_visualization()
        example_error_handling()
        example_performance_test()
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("Make sure all dependencies are installed: pip install -r requirements.txt")
    
    print("\n💡 Next Steps:")
    print("1. Set your OpenAI API key for full functionality")
    print("2. Try the system with your own data")
    print("3. Explore the DAG visualization features")
    print("4. Run the test suite: python test_system.py")


if __name__ == "__main__":
    main()
