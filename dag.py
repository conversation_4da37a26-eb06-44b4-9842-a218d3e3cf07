"""
DAG (Directed Acyclic Graph) module for tracking query relationships and context.

WHAT IS A DAG IN THIS CONTEXT?
==============================
A DAG is like a family tree for your queries. It tracks how queries relate to each other:

Example conversation:
1. "Show top 10 customers by revenue"           <- Root query
2. "Break that down by region"                 <- Child of query 1 (refers to "that")
3. "Which region has the highest average?"     <- Child of query 2 (refers to regions)
4. "Show me customers from that region"        <- Child of query 3 (refers to "that region")

The DAG looks like:
Query 1 → Query 2 → Query 3 → Query 4

WHY USE A DAG?
==============
- **Context Preservation**: Remembers what "that", "it", "those" refer to
- **Query Rewriting**: Converts "Break that down" → "Break down top 10 customers by region"
- **Smart Routing**: Understands if a follow-up needs SQL or advanced analytics
- **Conversation Flow**: Maintains logical flow of data exploration

DIRECTED: Arrows show dependency direction (Query 2 depends on Query 1)
ACYCLIC: No circular references (Query 4 can't refer back to Query 1)
GRAPH: Network of connected query nodes

This module provides the core data structures for maintaining this query history
and relationships in the Contextual NL2SQL system.
"""

from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime
import uuid


@dataclass
class QueryNode:
    """
    Represents a single query in the DAG with metadata and relationships.
    
    Attributes:
        id: Unique identifier for the query node
        original_query: The original natural language query from the user
        rewritten_query: The rewritten/contextualized version of the query
        sql_query: The generated SQL query (if applicable)
        result_summary: Summary of the query results
        timestamp: When the query was created
        filters: Any filters applied in this query
        groupings: Grouping criteria used
        aggregations: Aggregation functions applied
        metadata: Additional metadata for the query
    """
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    original_query: str = ""
    rewritten_query: Optional[str] = None
    sql_query: Optional[str] = None
    result_summary: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    filters: List[str] = field(default_factory=list)
    groupings: List[str] = field(default_factory=list)
    aggregations: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Ensure ID is always a string."""
        if not isinstance(self.id, str):
            self.id = str(self.id)


class QueryDAG:
    """
    Directed Acyclic Graph for managing query relationships and context.
    
    This class maintains the relationships between queries and provides
    methods for context retrieval and graph traversal.
    """
    
    def __init__(self):
        """Initialize an empty DAG."""
        self.nodes: Dict[str, QueryNode] = {}
        self.edges: Dict[str, List[str]] = {}  # parent_id -> [child_ids]
        self.edge_types: Dict[tuple, str] = {}  # (parent_id, child_id) -> edge_type
        self.current_node_id: Optional[str] = None
    
    def add_node(self, node: QueryNode, parent_id: Optional[str] = None, 
                 edge_type: str = "follows") -> str:
        """
        Add a new query node to the DAG.
        
        Args:
            node: The QueryNode to add
            parent_id: ID of the parent node (if any)
            edge_type: Type of relationship (follows, refers_to, filters, drills_down)
            
        Returns:
            The ID of the added node
        """
        self.nodes[node.id] = node
        self.current_node_id = node.id
        
        if parent_id and parent_id in self.nodes:
            self.add_edge(parent_id, node.id, edge_type)
        
        return node.id
    
    def add_edge(self, parent_id: str, child_id: str, edge_type: str = "follows"):
        """
        Add an edge between two nodes.
        
        Args:
            parent_id: ID of the parent node
            child_id: ID of the child node
            edge_type: Type of relationship
        """
        if parent_id not in self.nodes or child_id not in self.nodes:
            raise ValueError("Both nodes must exist in the DAG before adding an edge")
        
        if parent_id not in self.edges:
            self.edges[parent_id] = []
        
        if child_id not in self.edges[parent_id]:
            self.edges[parent_id].append(child_id)
            self.edge_types[(parent_id, child_id)] = edge_type
    
    def get_node(self, node_id: str) -> Optional[QueryNode]:
        """Get a node by its ID."""
        return self.nodes.get(node_id)
    
    def get_current_node(self) -> Optional[QueryNode]:
        """Get the current (most recent) node."""
        if self.current_node_id:
            return self.nodes.get(self.current_node_id)
        return None
    
    def get_parents(self, node_id: str) -> List[QueryNode]:
        """Get all parent nodes of a given node."""
        parents = []
        for parent_id, children in self.edges.items():
            if node_id in children:
                if parent_id in self.nodes:
                    parents.append(self.nodes[parent_id])
        return parents
    
    def get_children(self, node_id: str) -> List[QueryNode]:
        """Get all child nodes of a given node."""
        children = []
        if node_id in self.edges:
            for child_id in self.edges[node_id]:
                if child_id in self.nodes:
                    children.append(self.nodes[child_id])
        return children
    
    def get_upstream_context(self, node_id: str, max_depth: int = 2) -> List[QueryNode]:
        """
        Get upstream context nodes for query rewriting.
        
        Args:
            node_id: ID of the node to get context for
            max_depth: Maximum depth to traverse upstream
            
        Returns:
            List of upstream nodes ordered by relevance
        """
        context_nodes = []
        visited = set()
        
        def traverse_upstream(current_id: str, depth: int):
            if depth >= max_depth or current_id in visited:
                return
            
            visited.add(current_id)
            parents = self.get_parents(current_id)
            
            for parent in parents:
                if parent.id not in visited:
                    context_nodes.append(parent)
                    traverse_upstream(parent.id, depth + 1)
        
        traverse_upstream(node_id, 0)
        
        # Sort by timestamp (most recent first) and limit results
        context_nodes.sort(key=lambda x: x.timestamp, reverse=True)
        return context_nodes[:max_depth]
    
    def get_edge_type(self, parent_id: str, child_id: str) -> Optional[str]:
        """Get the type of edge between two nodes."""
        return self.edge_types.get((parent_id, child_id))
    
    def get_query_chain(self, node_id: str) -> List[QueryNode]:
        """
        Get the chain of queries leading to a specific node.
        
        Args:
            node_id: ID of the target node
            
        Returns:
            List of nodes in chronological order leading to the target
        """
        chain = []
        visited = set()
        
        def build_chain(current_id: str):
            if current_id in visited or current_id not in self.nodes:
                return
            
            visited.add(current_id)
            parents = self.get_parents(current_id)
            
            # Recursively build chain from parents
            for parent in parents:
                build_chain(parent.id)
            
            chain.append(self.nodes[current_id])
        
        build_chain(node_id)
        
        # Sort by timestamp to ensure chronological order
        chain.sort(key=lambda x: x.timestamp)
        return chain
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the DAG to a dictionary representation."""
        return {
            "nodes": {
                node_id: {
                    "id": node.id,
                    "original_query": node.original_query,
                    "rewritten_query": node.rewritten_query,
                    "sql_query": node.sql_query,
                    "result_summary": node.result_summary,
                    "timestamp": node.timestamp.isoformat(),
                    "filters": node.filters,
                    "groupings": node.groupings,
                    "aggregations": node.aggregations,
                    "metadata": node.metadata
                }
                for node_id, node in self.nodes.items()
            },
            "edges": self.edges,
            "edge_types": {
                f"{parent}->{child}": edge_type
                for (parent, child), edge_type in self.edge_types.items()
            },
            "current_node_id": self.current_node_id
        }
    
    def __len__(self) -> int:
        """Return the number of nodes in the DAG."""
        return len(self.nodes)
    
    def __contains__(self, node_id: str) -> bool:
        """Check if a node exists in the DAG."""
        return node_id in self.nodes
