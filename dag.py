"""
DAG (Directed Acyclic Graph) module for tracking query relationships and context.

WHAT IS A DAG IN THIS CONTEXT?
==============================
A DAG is like a family tree for your queries. It tracks how queries relate to each other:

Example conversation:
1. "Show top 10 customers by revenue"           <- Root query
2. "Break that down by region"                 <- Child of query 1 (refers to "that")
3. "Which region has the highest average?"     <- Child of query 2 (refers to regions)
4. "Show me customers from that region"        <- Child of query 3 (refers to "that region")

The DAG looks like:
Query 1 → Query 2 → Query 3 → Query 4

WHY USE A DAG?
==============
- **Context Preservation**: Remembers what "that", "it", "those" refer to
- **Query Rewriting**: Converts "Break that down" → "Break down top 10 customers by region"
- **Smart Routing**: Understands if a follow-up needs SQL or advanced analytics
- **Conversation Flow**: Maintains logical flow of data exploration

DIRECTED: Arrows show dependency direction (Query 2 depends on Query 1)
ACYCLIC: No circular references (Query 4 can't refer back to Query 1)
GRAPH: Network of connected query nodes

This module provides the core data structures for maintaining this query history
and relationships in the Contextual NL2SQL system.
"""

from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime
import uuid
import networkx as nx
import json
import os
from pathlib import Path


@dataclass
class QueryNode:
    """
    Represents a single query in the DAG with metadata and relationships.
    
    Attributes:
        id: Unique identifier for the query node
        original_query: The original natural language query from the user
        rewritten_query: The rewritten/contextualized version of the query
        sql_query: The generated SQL query (if applicable)
        result_summary: Summary of the query results
        timestamp: When the query was created
        filters: Any filters applied in this query
        groupings: Grouping criteria used
        aggregations: Aggregation functions applied
        metadata: Additional metadata for the query
    """
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    original_query: str = ""
    rewritten_query: Optional[str] = None
    sql_query: Optional[str] = None
    result_summary: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    filters: List[str] = field(default_factory=list)
    groupings: List[str] = field(default_factory=list)
    aggregations: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Ensure ID is always a string."""
        if not isinstance(self.id, str):
            self.id = str(self.id)


class QueryDAG:
    """
    NetworkX-based Directed Acyclic Graph for managing query relationships and context.

    This class uses NetworkX to maintain relationships between queries and provides
    methods for context retrieval, graph traversal, and persistence.

    Features:
    - NetworkX DiGraph for robust graph operations
    - JSON persistence for saving/loading DAG state
    - Advanced graph algorithms (shortest paths, centrality, etc.)
    - Visualization capabilities
    """

    def __init__(self, persistence_file: Optional[str] = None):
        """
        Initialize a NetworkX-based DAG.

        Args:
            persistence_file: Optional file path for saving/loading DAG state
        """
        self.graph = nx.DiGraph()  # NetworkX Directed Graph
        self.current_node_id: Optional[str] = None
        self.persistence_file = persistence_file or "query_dag.json"

        # Load existing DAG if persistence file exists
        if os.path.exists(self.persistence_file):
            self.load_from_file()
    
    def add_node(self, node: QueryNode, parent_id: Optional[str] = None,
                 edge_type: str = "follows") -> str:
        """
        Add a new query node to the NetworkX DAG.

        Args:
            node: The QueryNode to add
            parent_id: ID of the parent node (if any)
            edge_type: Type of relationship (follows, refers_to, filters, drills_down)

        Returns:
            The ID of the added node
        """
        # Add node to NetworkX graph with all node attributes
        self.graph.add_node(node.id,
                           query_node=node,
                           original_query=node.original_query,
                           rewritten_query=node.rewritten_query,
                           sql_query=node.sql_query,
                           result_summary=node.result_summary,
                           timestamp=node.timestamp.isoformat(),
                           filters=node.filters,
                           groupings=node.groupings,
                           aggregations=node.aggregations,
                           metadata=node.metadata)

        self.current_node_id = node.id

        # Add edge if parent specified
        if parent_id and parent_id in self.graph:
            self.add_edge(parent_id, node.id, edge_type)

        # Auto-save if persistence is enabled
        self.save_to_file()

        return node.id

    def add_edge(self, parent_id: str, child_id: str, edge_type: str = "follows"):
        """
        Add an edge between two nodes in the NetworkX graph.

        Args:
            parent_id: ID of the parent node
            child_id: ID of the child node
            edge_type: Type of relationship
        """
        if parent_id not in self.graph or child_id not in self.graph:
            raise ValueError("Both nodes must exist in the DAG before adding an edge")

        # Add edge with type attribute
        self.graph.add_edge(parent_id, child_id, edge_type=edge_type)

        # Check for cycles (DAG must be acyclic)
        if not nx.is_directed_acyclic_graph(self.graph):
            # Remove the edge that created the cycle
            self.graph.remove_edge(parent_id, child_id)
            raise ValueError(f"Adding edge {parent_id} -> {child_id} would create a cycle")

        # Auto-save if persistence is enabled
        self.save_to_file()
    
    def get_node(self, node_id: str) -> Optional[QueryNode]:
        """Get a node by its ID from NetworkX graph."""
        if node_id in self.graph:
            return self.graph.nodes[node_id]['query_node']
        return None

    def get_current_node(self) -> Optional[QueryNode]:
        """Get the current (most recent) node."""
        if self.current_node_id and self.current_node_id in self.graph:
            return self.graph.nodes[self.current_node_id]['query_node']
        return None

    def get_parents(self, node_id: str) -> List[QueryNode]:
        """Get all parent nodes of a given node using NetworkX."""
        if node_id not in self.graph:
            return []

        parents = []
        for parent_id in self.graph.predecessors(node_id):
            parent_node = self.graph.nodes[parent_id]['query_node']
            parents.append(parent_node)
        return parents

    def get_children(self, node_id: str) -> List[QueryNode]:
        """Get all child nodes of a given node using NetworkX."""
        if node_id not in self.graph:
            return []

        children = []
        for child_id in self.graph.successors(node_id):
            child_node = self.graph.nodes[child_id]['query_node']
            children.append(child_node)
        return children
    
    def get_upstream_context(self, node_id: str, max_depth: int = 2) -> List[QueryNode]:
        """
        Get upstream context nodes for query rewriting using NetworkX algorithms.

        Args:
            node_id: ID of the node to get context for
            max_depth: Maximum depth to traverse upstream

        Returns:
            List of upstream nodes ordered by relevance
        """
        if node_id not in self.graph:
            return []

        context_nodes = []

        # Use NetworkX to get all ancestors within max_depth
        try:
            # Get all nodes within max_depth using BFS
            for depth in range(1, max_depth + 1):
                # Get nodes at exactly this depth
                ancestors_at_depth = []
                for ancestor_id in self.graph.predecessors(node_id):
                    if depth == 1:
                        ancestors_at_depth.append(ancestor_id)
                    else:
                        # For deeper levels, use shortest path length
                        try:
                            path_length = nx.shortest_path_length(self.graph, ancestor_id, node_id)
                            if path_length == depth:
                                ancestors_at_depth.append(ancestor_id)
                        except nx.NetworkXNoPath:
                            continue

                # Add nodes at this depth to context
                for ancestor_id in ancestors_at_depth:
                    ancestor_node = self.graph.nodes[ancestor_id]['query_node']
                    context_nodes.append(ancestor_node)

        except Exception:
            # Fallback to simple parent traversal
            parents = self.get_parents(node_id)
            context_nodes.extend(parents[:max_depth])

        # Sort by timestamp (most recent first) and limit results
        context_nodes.sort(key=lambda x: x.timestamp, reverse=True)
        return context_nodes[:max_depth]
    
    def get_edge_type(self, parent_id: str, child_id: str) -> Optional[str]:
        """Get the type of edge between two nodes from NetworkX graph."""
        if self.graph.has_edge(parent_id, child_id):
            return self.graph.edges[parent_id, child_id].get('edge_type', 'follows')
        return None
    
    def get_query_chain(self, node_id: str) -> List[QueryNode]:
        """
        Get the chain of queries leading to a specific node using NetworkX.

        Args:
            node_id: ID of the target node

        Returns:
            List of nodes in chronological order leading to the target
        """
        if node_id not in self.graph:
            return []

        chain = []

        # Find all ancestors using NetworkX
        try:
            ancestors = nx.ancestors(self.graph, node_id)
            ancestors.add(node_id)  # Include the target node itself

            # Get QueryNode objects for all ancestors
            for ancestor_id in ancestors:
                ancestor_node = self.graph.nodes[ancestor_id]['query_node']
                chain.append(ancestor_node)

            # Sort by timestamp to ensure chronological order
            chain.sort(key=lambda x: x.timestamp)

        except Exception:
            # Fallback: just return the single node
            target_node = self.get_node(node_id)
            if target_node:
                chain = [target_node]

        return chain
    
    def save_to_file(self, filename: Optional[str] = None):
        """
        Save the NetworkX DAG to a JSON file.

        Args:
            filename: Optional filename override
        """
        try:
            save_file = filename or self.persistence_file

            # Convert NetworkX graph to JSON-serializable format
            graph_data = {
                "nodes": {},
                "edges": [],
                "current_node_id": self.current_node_id
            }

            # Save nodes with all attributes
            for node_id in self.graph.nodes():
                node_data = self.graph.nodes[node_id]
                query_node = node_data['query_node']

                graph_data["nodes"][node_id] = {
                    "id": query_node.id,
                    "original_query": query_node.original_query,
                    "rewritten_query": query_node.rewritten_query,
                    "sql_query": query_node.sql_query,
                    "result_summary": query_node.result_summary,
                    "timestamp": query_node.timestamp.isoformat(),
                    "filters": query_node.filters,
                    "groupings": query_node.groupings,
                    "aggregations": query_node.aggregations,
                    "metadata": query_node.metadata
                }

            # Save edges with types
            for parent_id, child_id in self.graph.edges():
                edge_data = self.graph.edges[parent_id, child_id]
                graph_data["edges"].append({
                    "parent": parent_id,
                    "child": child_id,
                    "edge_type": edge_data.get("edge_type", "follows")
                })

            # Write to file
            with open(save_file, 'w') as f:
                json.dump(graph_data, f, indent=2)

        except Exception as e:
            print(f"Warning: Could not save DAG to file: {e}")

    def load_from_file(self, filename: Optional[str] = None):
        """
        Load the NetworkX DAG from a JSON file.

        Args:
            filename: Optional filename override
        """
        try:
            load_file = filename or self.persistence_file

            if not os.path.exists(load_file):
                return

            with open(load_file, 'r') as f:
                graph_data = json.load(f)

            # Clear existing graph
            self.graph.clear()

            # Recreate nodes
            for node_id, node_data in graph_data.get("nodes", {}).items():
                # Recreate QueryNode
                query_node = QueryNode(
                    id=node_data["id"],
                    original_query=node_data["original_query"],
                    rewritten_query=node_data.get("rewritten_query"),
                    sql_query=node_data.get("sql_query"),
                    result_summary=node_data.get("result_summary"),
                    timestamp=datetime.fromisoformat(node_data["timestamp"]),
                    filters=node_data.get("filters", []),
                    groupings=node_data.get("groupings", []),
                    aggregations=node_data.get("aggregations", []),
                    metadata=node_data.get("metadata", {})
                )

                # Add to NetworkX graph
                self.graph.add_node(node_id,
                                   query_node=query_node,
                                   original_query=query_node.original_query,
                                   rewritten_query=query_node.rewritten_query,
                                   sql_query=query_node.sql_query,
                                   result_summary=query_node.result_summary,
                                   timestamp=query_node.timestamp.isoformat(),
                                   filters=query_node.filters,
                                   groupings=query_node.groupings,
                                   aggregations=query_node.aggregations,
                                   metadata=query_node.metadata)

            # Recreate edges
            for edge_data in graph_data.get("edges", []):
                parent_id = edge_data["parent"]
                child_id = edge_data["child"]
                edge_type = edge_data.get("edge_type", "follows")

                if parent_id in self.graph and child_id in self.graph:
                    self.graph.add_edge(parent_id, child_id, edge_type=edge_type)

            # Restore current node
            self.current_node_id = graph_data.get("current_node_id")

        except Exception as e:
            print(f"Warning: Could not load DAG from file: {e}")

    def to_dict(self) -> Dict[str, Any]:
        """Convert the NetworkX DAG to a dictionary representation."""
        return {
            "nodes": {
                node_id: dict(self.graph.nodes[node_id])
                for node_id in self.graph.nodes()
            },
            "edges": [
                {
                    "parent": parent,
                    "child": child,
                    "edge_type": self.graph.edges[parent, child].get("edge_type", "follows")
                }
                for parent, child in self.graph.edges()
            ],
            "current_node_id": self.current_node_id,
            "graph_info": {
                "num_nodes": self.graph.number_of_nodes(),
                "num_edges": self.graph.number_of_edges(),
                "is_dag": nx.is_directed_acyclic_graph(self.graph)
            }
        }

    def get_graph_statistics(self) -> Dict[str, Any]:
        """Get NetworkX graph statistics and analysis."""
        if self.graph.number_of_nodes() == 0:
            return {"message": "Empty graph"}

        stats = {
            "num_nodes": self.graph.number_of_nodes(),
            "num_edges": self.graph.number_of_edges(),
            "is_dag": nx.is_directed_acyclic_graph(self.graph),
            "density": nx.density(self.graph),
        }

        # Add more advanced statistics if graph is not empty
        try:
            if self.graph.number_of_nodes() > 1:
                stats.update({
                    "average_clustering": nx.average_clustering(self.graph.to_undirected()),
                    "number_of_weakly_connected_components": nx.number_weakly_connected_components(self.graph)
                })

            # Find root nodes (no predecessors)
            root_nodes = [n for n in self.graph.nodes() if self.graph.in_degree(n) == 0]
            stats["root_nodes"] = len(root_nodes)

            # Find leaf nodes (no successors)
            leaf_nodes = [n for n in self.graph.nodes() if self.graph.out_degree(n) == 0]
            stats["leaf_nodes"] = len(leaf_nodes)

        except Exception as e:
            stats["analysis_error"] = str(e)

        return stats

    def visualize_graph(self, save_path: Optional[str] = None) -> str:
        """
        Create a visualization of the DAG using NetworkX and matplotlib.

        Args:
            save_path: Optional path to save the visualization

        Returns:
            Path to the saved visualization or status message
        """
        try:
            import matplotlib.pyplot as plt

            if self.graph.number_of_nodes() == 0:
                return "Cannot visualize empty graph"

            plt.figure(figsize=(12, 8))

            # Use hierarchical layout for DAG
            try:
                pos = nx.spring_layout(self.graph, k=2, iterations=50)
            except:
                pos = nx.random_layout(self.graph)

            # Draw nodes
            nx.draw_networkx_nodes(self.graph, pos,
                                 node_color='lightblue',
                                 node_size=1000,
                                 alpha=0.7)

            # Draw edges with different colors for different types
            edge_colors = []
            for parent, child in self.graph.edges():
                edge_type = self.graph.edges[parent, child].get('edge_type', 'follows')
                if edge_type == 'follows':
                    edge_colors.append('blue')
                elif edge_type == 'filters':
                    edge_colors.append('red')
                elif edge_type == 'groups':
                    edge_colors.append('green')
                else:
                    edge_colors.append('gray')

            nx.draw_networkx_edges(self.graph, pos,
                                 edge_color=edge_colors,
                                 arrows=True,
                                 arrowsize=20,
                                 alpha=0.6)

            # Add labels (shortened queries)
            labels = {}
            for node_id in self.graph.nodes():
                query = self.graph.nodes[node_id]['original_query']
                # Truncate long queries
                labels[node_id] = query[:20] + "..." if len(query) > 20 else query

            nx.draw_networkx_labels(self.graph, pos, labels, font_size=8)

            plt.title("Query DAG Visualization")
            plt.axis('off')

            # Save or show
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                plt.close()
                return f"Visualization saved to {save_path}"
            else:
                default_path = "query_dag_visualization.png"
                plt.savefig(default_path, dpi=300, bbox_inches='tight')
                plt.close()
                return f"Visualization saved to {default_path}"

        except ImportError:
            return "matplotlib not available for visualization"
        except Exception as e:
            return f"Error creating visualization: {e}"

    def __len__(self) -> int:
        """Return the number of nodes in the NetworkX DAG."""
        return self.graph.number_of_nodes()

    def __contains__(self, node_id: str) -> bool:
        """Check if a node exists in the NetworkX DAG."""
        return node_id in self.graph
