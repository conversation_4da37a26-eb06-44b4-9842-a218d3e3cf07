"""
NetworkX DAG Implementation Demo

This script demonstrates the new NetworkX-based DAG implementation
with persistence, graph analysis, and visualization features.
"""

from dag import QueryNode, QueryDAG
import pandas as pd
from datetime import datetime, timedelta
import os


def demo_basic_networkx_features():
    """Demonstrate basic NetworkX DAG features."""
    print("🔗 NetworkX DAG Implementation Demo")
    print("=" * 40)
    
    # Create DAG with persistence
    dag = QueryDAG(persistence_file="demo_dag.json")
    
    print(f"✅ Created NetworkX DAG")
    print(f"   Persistence file: demo_dag.json")
    print(f"   Initial nodes: {len(dag)}")
    
    # Create sample queries
    queries = [
        "Show all customers",
        "Filter customers with revenue > 10000", 
        "Group those by region",
        "Which region has highest average revenue?",
        "Show customers from that region"
    ]
    
    print(f"\n📝 Adding {len(queries)} queries to DAG...")
    
    previous_id = None
    for i, query in enumerate(queries):
        node = QueryNode(
            original_query=query,
            timestamp=datetime.now() + timedelta(seconds=i)
        )
        
        # Add some realistic metadata
        if "filter" in query.lower():
            node.filters = ["revenue > 10000"]
        elif "group" in query.lower():
            node.groupings = ["region"]
        elif "average" in query.lower():
            node.aggregations = ["AVG(revenue)"]
        
        node_id = dag.add_node(node, parent_id=previous_id, edge_type="follows")
        previous_id = node_id
        
        print(f"   Added: {query[:30]}... (ID: {node_id[:8]}...)")
    
    print(f"\n📊 DAG Statistics:")
    stats = dag.get_graph_statistics()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    return dag


def demo_graph_analysis(dag):
    """Demonstrate NetworkX graph analysis features."""
    print(f"\n🔍 Graph Analysis Features")
    print("-" * 30)
    
    # Get current node and analyze context
    current_node = dag.get_current_node()
    if current_node:
        print(f"Current query: {current_node.original_query}")
        
        # Get upstream context
        context = dag.get_upstream_context(current_node.id, max_depth=3)
        print(f"\nUpstream context ({len(context)} nodes):")
        for i, node in enumerate(context, 1):
            print(f"   {i}. {node.original_query}")
            if node.filters:
                print(f"      Filters: {node.filters}")
            if node.groupings:
                print(f"      Groupings: {node.groupings}")
    
    # Analyze query chains
    print(f"\n🔗 Query Chain Analysis:")
    if current_node:
        chain = dag.get_query_chain(current_node.id)
        print(f"Complete chain to current query ({len(chain)} nodes):")
        for i, node in enumerate(chain, 1):
            print(f"   {i}. {node.original_query}")
    
    # Show relationships
    print(f"\n🌐 Relationship Analysis:")
    dag_dict = dag.to_dict()
    for edge in dag_dict["edges"]:
        parent_query = dag.get_node(edge["parent"]).original_query[:25] + "..."
        child_query = dag.get_node(edge["child"]).original_query[:25] + "..."
        edge_type = edge["edge_type"]
        print(f"   '{parent_query}' --({edge_type})--> '{child_query}'")


def demo_persistence_features(dag):
    """Demonstrate persistence and file operations."""
    print(f"\n💾 Persistence Features")
    print("-" * 25)
    
    # Save current state
    dag.save_to_file("backup_dag.json")
    print("✅ Saved DAG to backup_dag.json")
    
    # Create new DAG and load from file
    new_dag = QueryDAG(persistence_file="backup_dag.json")
    print(f"✅ Loaded DAG from file: {len(new_dag)} nodes")
    
    # Verify data integrity
    original_stats = dag.get_graph_statistics()
    loaded_stats = new_dag.get_graph_statistics()
    
    print(f"\n🔍 Data Integrity Check:")
    print(f"   Original nodes: {original_stats['num_nodes']}")
    print(f"   Loaded nodes: {loaded_stats['num_nodes']}")
    print(f"   Original edges: {original_stats['num_edges']}")
    print(f"   Loaded edges: {loaded_stats['num_edges']}")
    print(f"   Integrity: {'✅ PASS' if original_stats['num_nodes'] == loaded_stats['num_nodes'] else '❌ FAIL'}")
    
    return new_dag


def demo_advanced_networkx_features(dag):
    """Demonstrate advanced NetworkX features."""
    print(f"\n🚀 Advanced NetworkX Features")
    print("-" * 35)
    
    # Create a more complex DAG structure
    print("Creating complex branching structure...")
    
    # Get root node
    root_nodes = [n for n in dag.graph.nodes() if dag.graph.in_degree(n) == 0]
    if root_nodes:
        root_id = root_nodes[0]
        
        # Create branching paths
        branch1_node = QueryNode(original_query="Analyze revenue trends")
        branch1_id = dag.add_node(branch1_node, parent_id=root_id, edge_type="analyzes")
        
        branch2_node = QueryNode(original_query="Analyze customer demographics")
        branch2_id = dag.add_node(branch2_node, parent_id=root_id, edge_type="analyzes")
        
        # Convergence node
        convergence_node = QueryNode(original_query="Compare revenue vs demographics insights")
        convergence_id = dag.add_node(convergence_node, parent_id=branch1_id, edge_type="compares")
        dag.add_edge(branch2_id, convergence_id, edge_type="compares")
        
        print("✅ Created branching and convergence structure")
    
    # Updated statistics
    stats = dag.get_graph_statistics()
    print(f"\n📊 Updated Graph Statistics:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # NetworkX-specific analysis
    print(f"\n🔬 NetworkX Analysis:")
    try:
        import networkx as nx
        
        # Find shortest paths
        if len(dag.graph) > 2:
            nodes = list(dag.graph.nodes())
            if len(nodes) >= 2:
                try:
                    path_length = nx.shortest_path_length(dag.graph, nodes[0], nodes[-1])
                    print(f"   Shortest path from first to last node: {path_length}")
                except:
                    print("   No path between first and last node")
        
        # Topological sort (useful for DAGs)
        try:
            topo_order = list(nx.topological_sort(dag.graph))
            print(f"   Topological order: {len(topo_order)} nodes")
            print(f"   First 3 nodes in topo order: {[node[:8] + '...' for node in topo_order[:3]]}")
        except:
            print("   Could not compute topological sort")
        
        # Graph density
        density = nx.density(dag.graph)
        print(f"   Graph density: {density:.3f}")
        
    except ImportError:
        print("   NetworkX not available for advanced analysis")


def demo_visualization(dag):
    """Demonstrate DAG visualization."""
    print(f"\n🎨 Visualization Features")
    print("-" * 27)
    
    try:
        # Create visualization
        result = dag.visualize_graph("demo_dag_visualization.png")
        print(f"✅ {result}")
        
        # Show what the visualization contains
        print(f"\n📊 Visualization includes:")
        print(f"   • {len(dag)} query nodes")
        print(f"   • {dag.graph.number_of_edges()} relationships")
        print(f"   • Color-coded edge types")
        print(f"   • Hierarchical layout")
        
    except Exception as e:
        print(f"❌ Visualization failed: {e}")
        print("   (This is normal if matplotlib is not installed)")


def demo_real_world_scenario():
    """Demonstrate a real-world data analysis scenario."""
    print(f"\n🌍 Real-World Scenario Demo")
    print("-" * 35)
    
    # Create a realistic data analysis conversation
    dag = QueryDAG(persistence_file="real_world_dag.json")
    
    conversation = [
        ("Show sales data for last quarter", "initial_query"),
        ("Filter for high-value customers only", "filters"),
        ("Break that down by product category", "groups"),
        ("Which category has the best margins?", "analyzes"),
        ("Show trend analysis for that category", "analyzes"),
        ("What factors might explain this trend?", "reasons"),
        ("Create a visualization of the findings", "visualizes")
    ]
    
    print("Simulating data analysis conversation:")
    
    previous_id = None
    for i, (query, edge_type) in enumerate(conversation, 1):
        print(f"\n{i}. User: {query}")
        
        node = QueryNode(
            original_query=query,
            timestamp=datetime.now() + timedelta(minutes=i)
        )
        
        # Add realistic metadata based on query type
        if "filter" in query.lower():
            node.filters = ["customer_value > 10000"]
        elif "break" in query.lower() or "category" in query.lower():
            node.groupings = ["product_category"]
        elif "margin" in query.lower():
            node.aggregations = ["AVG(margin)", "SUM(revenue)"]
        elif "trend" in query.lower():
            node.metadata = {"analysis_type": "time_series", "requires_pandas_ai": True}
        elif "factor" in query.lower() or "explain" in query.lower():
            node.metadata = {"analysis_type": "causal_analysis", "requires_pandas_ai": True}
        elif "visualization" in query.lower():
            node.metadata = {"output_type": "chart", "requires_pandas_ai": True}
        
        node_id = dag.add_node(node, parent_id=previous_id, edge_type=edge_type)
        previous_id = node_id
        
        # Show context that would be available for query rewriting
        if i > 1:
            context = dag.get_upstream_context(node_id, max_depth=2)
            print(f"   Context available: {len(context)} previous queries")
            if context:
                print(f"   Most recent: '{context[0].original_query[:40]}...'")
    
    print(f"\n📊 Final conversation statistics:")
    stats = dag.get_graph_statistics()
    print(f"   Total queries: {stats['num_nodes']}")
    print(f"   Relationships: {stats['num_edges']}")
    print(f"   Analysis depth: {stats['num_edges']} levels")
    
    # Show how this helps with query rewriting
    current_node = dag.get_current_node()
    if current_node:
        print(f"\n💡 Query Rewriting Example:")
        print(f"   Original: '{current_node.original_query}'")
        context = dag.get_upstream_context(current_node.id, max_depth=3)
        print(f"   Available context: {len(context)} previous queries")
        print(f"   → System can rewrite 'the findings' to refer to specific category trends")
        print(f"   → System knows this needs visualization (Pandas AI routing)")
    
    return dag


def cleanup_demo_files():
    """Clean up demo files."""
    demo_files = [
        "demo_dag.json",
        "backup_dag.json", 
        "real_world_dag.json",
        "demo_dag_visualization.png"
    ]
    
    print(f"\n🧹 Cleaning up demo files...")
    for file in demo_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"   Removed: {file}")


def main():
    """Run all NetworkX DAG demos."""
    print("🚀 NetworkX DAG Implementation - Complete Demo")
    print("=" * 55)
    
    try:
        # Basic features
        dag = demo_basic_networkx_features()
        
        # Graph analysis
        demo_graph_analysis(dag)
        
        # Persistence
        loaded_dag = demo_persistence_features(dag)
        
        # Advanced features
        demo_advanced_networkx_features(loaded_dag)
        
        # Visualization
        demo_visualization(loaded_dag)
        
        # Real-world scenario
        real_dag = demo_real_world_scenario()
        
        print(f"\n✅ All NetworkX demos completed successfully!")
        
        print(f"\n🎯 Key Benefits of NetworkX Implementation:")
        print(f"   ✅ True graph database functionality")
        print(f"   ✅ Automatic cycle detection (maintains DAG property)")
        print(f"   ✅ Persistent storage with JSON serialization")
        print(f"   ✅ Advanced graph algorithms and analysis")
        print(f"   ✅ Built-in visualization capabilities")
        print(f"   ✅ Scalable for complex query relationships")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        cleanup_demo_files()


if __name__ == "__main__":
    main()
