"""
Unit tests for the Contextual NL2SQL System.

This module contains comprehensive tests for all components of the system.
"""

import unittest
import pandas as pd
from datetime import datetime
import os
from unittest.mock import Mock, patch

from dag import QueryNode, QueryDAG
from rewriter import QueryRewriter
from pandas_ai_runner import PandasAIRunner
from main import ContextualNL2SQLSystem


class TestQueryNode(unittest.TestCase):
    """Test cases for QueryNode class."""
    
    def test_query_node_creation(self):
        """Test basic QueryNode creation."""
        node = QueryNode(original_query="Show top customers")
        
        self.assertIsInstance(node.id, str)
        self.assertEqual(node.original_query, "Show top customers")
        self.assertIsInstance(node.timestamp, datetime)
        self.assertEqual(node.filters, [])
        self.assertEqual(node.groupings, [])
        self.assertEqual(node.aggregations, [])
    
    def test_query_node_with_metadata(self):
        """Test QueryNode with additional metadata."""
        node = QueryNode(
            original_query="Filter by region",
            filters=["region = 'North'"],
            groupings=["region"],
            metadata={"source": "test"}
        )
        
        self.assertEqual(node.filters, ["region = 'North'"])
        self.assertEqual(node.groupings, ["region"])
        self.assertEqual(node.metadata["source"], "test")


class TestQueryDAG(unittest.TestCase):
    """Test cases for QueryDAG class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.dag = QueryDAG()
        self.node1 = QueryNode(original_query="Query 1")
        self.node2 = QueryNode(original_query="Query 2")
    
    def test_add_node(self):
        """Test adding nodes to DAG."""
        node_id = self.dag.add_node(self.node1)
        
        self.assertEqual(len(self.dag), 1)
        self.assertIn(node_id, self.dag)
        self.assertEqual(self.dag.current_node_id, node_id)
    
    def test_add_node_with_parent(self):
        """Test adding node with parent relationship."""
        parent_id = self.dag.add_node(self.node1)
        child_id = self.dag.add_node(self.node2, parent_id=parent_id)

        self.assertEqual(len(self.dag), 2)

        # Check parent-child relationship using NetworkX
        children = self.dag.get_children(parent_id)
        self.assertEqual(len(children), 1)
        self.assertEqual(children[0].id, child_id)

        parents = self.dag.get_parents(child_id)
        self.assertEqual(len(parents), 1)
        self.assertEqual(parents[0].id, parent_id)

        # Verify NetworkX graph structure
        self.assertTrue(self.dag.graph.has_edge(parent_id, child_id))
        self.assertTrue(parent_id in self.dag.graph)
        self.assertTrue(child_id in self.dag.graph)
    
    def test_get_upstream_context(self):
        """Test upstream context retrieval."""
        # Create a chain: node1 -> node2 -> node3
        id1 = self.dag.add_node(self.node1)
        id2 = self.dag.add_node(self.node2, parent_id=id1)
        node3 = QueryNode(original_query="Query 3")
        id3 = self.dag.add_node(node3, parent_id=id2)
        
        # Get context for node3
        context = self.dag.get_upstream_context(id3, max_depth=2)
        
        self.assertLessEqual(len(context), 2)
        # Should include node2 and node1
        context_queries = [node.original_query for node in context]
        self.assertIn("Query 2", context_queries)
    
    def test_dag_serialization(self):
        """Test NetworkX DAG to dictionary conversion."""
        self.dag.add_node(self.node1)
        self.dag.add_node(self.node2, parent_id=self.node1.id)

        dag_dict = self.dag.to_dict()

        self.assertIn("nodes", dag_dict)
        self.assertIn("edges", dag_dict)
        self.assertIn("graph_info", dag_dict)
        self.assertEqual(len(dag_dict["nodes"]), 2)
        self.assertEqual(dag_dict["graph_info"]["num_nodes"], 2)
        self.assertTrue(dag_dict["graph_info"]["is_dag"])

    def test_networkx_persistence(self):
        """Test NetworkX DAG persistence to file."""
        import tempfile
        import os

        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            temp_file = f.name

        try:
            # Create DAG with persistence
            dag_with_persistence = QueryDAG(persistence_file=temp_file)
            dag_with_persistence.add_node(self.node1)
            dag_with_persistence.add_node(self.node2, parent_id=self.node1.id)

            # Verify file was created
            self.assertTrue(os.path.exists(temp_file))

            # Create new DAG and load from file
            new_dag = QueryDAG(persistence_file=temp_file)

            # Verify data was loaded correctly
            self.assertEqual(len(new_dag), 2)
            self.assertIn(self.node1.id, new_dag)
            self.assertIn(self.node2.id, new_dag)

            # Verify relationships
            children = new_dag.get_children(self.node1.id)
            self.assertEqual(len(children), 1)
            self.assertEqual(children[0].id, self.node2.id)

        finally:
            # Clean up
            if os.path.exists(temp_file):
                os.unlink(temp_file)

    def test_networkx_graph_statistics(self):
        """Test NetworkX graph statistics."""
        self.dag.add_node(self.node1)
        self.dag.add_node(self.node2, parent_id=self.node1.id)

        stats = self.dag.get_graph_statistics()

        self.assertEqual(stats["num_nodes"], 2)
        self.assertEqual(stats["num_edges"], 1)
        self.assertTrue(stats["is_dag"])
        self.assertEqual(stats["root_nodes"], 1)
        self.assertEqual(stats["leaf_nodes"], 1)

    def test_cycle_detection(self):
        """Test that NetworkX prevents cycles in DAG."""
        # Create a chain: node1 -> node2 -> node3
        id1 = self.dag.add_node(self.node1)
        id2 = self.dag.add_node(self.node2, parent_id=id1)
        node3 = QueryNode(original_query="Query 3")
        id3 = self.dag.add_node(node3, parent_id=id2)

        # Try to create a cycle: node3 -> node1 (should fail)
        with self.assertRaises(ValueError):
            self.dag.add_edge(id3, id1)


class TestQueryRewriter(unittest.TestCase):
    """Test cases for QueryRewriter class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Mock the OpenAI client to avoid API calls in tests
        self.mock_api_key = "test-key"
    
    @patch('openai.OpenAI')
    def test_rewriter_initialization(self, mock_openai):
        """Test QueryRewriter initialization."""
        rewriter = QueryRewriter(api_key=self.mock_api_key)
        
        self.assertEqual(rewriter.api_key, self.mock_api_key)
        self.assertEqual(rewriter.model, "gpt-4")
        mock_openai.assert_called_once()
    
    @patch('openai.OpenAI')
    def test_should_use_pandas_ai_with_llm(self, mock_openai):
        """Test LLM-based Pandas AI usage detection."""
        # Mock the OpenAI client and response
        mock_client = Mock()
        mock_openai.return_value = mock_client

        # Mock response for Pandas AI decision
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "PANDAS_AI - requires trend analysis"
        mock_client.chat.completions.create.return_value = mock_response

        rewriter = QueryRewriter(api_key=self.mock_api_key)
        rewriter.client = mock_client

        result = rewriter.should_use_pandas_ai("Show me trends in the data", True)
        self.assertTrue(result)

        # Verify LLM was called
        mock_client.chat.completions.create.assert_called_once()

    @patch('openai.OpenAI')
    def test_should_use_pandas_ai_sql_decision(self, mock_openai):
        """Test LLM-based SQL usage detection."""
        # Mock the OpenAI client and response
        mock_client = Mock()
        mock_openai.return_value = mock_client

        # Mock response for SQL decision
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "SQL - simple aggregation"
        mock_client.chat.completions.create.return_value = mock_response

        rewriter = QueryRewriter(api_key=self.mock_api_key)
        rewriter.client = mock_client

        result = rewriter.should_use_pandas_ai("Show top 10 customers", True)
        self.assertFalse(result)

    def test_fallback_routing_decision(self):
        """Test fallback routing when LLM fails."""
        rewriter = QueryRewriter(api_key=self.mock_api_key)

        # Test fallback for analytical queries
        analytical_queries = [
            "What trends do you see?",
            "Why did sales drop?",
            "Show me patterns and anomalies"
        ]

        for query in analytical_queries:
            result = rewriter._fallback_routing_decision(query)
            self.assertTrue(result, f"Should use Pandas AI for: {query}")

        # Test fallback for SQL queries
        sql_queries = [
            "SELECT top 10",
            "Filter by region",
            "Count customers"
        ]

        for query in sql_queries:
            result = rewriter._fallback_routing_decision(query)
            self.assertFalse(result, f"Should use SQL for: {query}")


class TestPandasAIRunner(unittest.TestCase):
    """Test cases for PandasAIRunner class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_data = pd.DataFrame({
            'customer': ['A', 'B', 'C'],
            'revenue': [100, 200, 300],
            'region': ['North', 'South', 'East']
        })
    
    @patch('pandasai.Agent')
    @patch('pandasai.llm.OpenAI')
    def test_pandas_ai_initialization(self, mock_llm, mock_agent):
        """Test PandasAIRunner initialization."""
        runner = PandasAIRunner(api_key="test-key")
        
        self.assertEqual(runner.api_key, "test-key")
        self.assertEqual(runner.model, "gpt-4")
        mock_llm.assert_called_once()
    
    def test_data_summary(self):
        """Test data summary generation."""
        runner = PandasAIRunner(api_key="test-key")
        summary = runner._get_data_summary(self.sample_data)
        
        self.assertEqual(summary["shape"], (3, 3))
        self.assertEqual(len(summary["columns"]), 3)
        self.assertIn("customer", summary["columns"])
        self.assertIn("revenue", summary["columns"])


class TestContextualNL2SQLSystem(unittest.TestCase):
    """Test cases for the main system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_data = pd.DataFrame({
            'customer_id': [1, 2, 3],
            'revenue': [1000, 2000, 3000],
            'region': ['North', 'South', 'East']
        })
    
    def test_system_initialization_without_api_key(self):
        """Test system initialization without API key."""
        system = ContextualNL2SQLSystem()
        
        self.assertIsInstance(system.dag, QueryDAG)
        self.assertIsNone(system.rewriter)
        self.assertIsNone(system.pandas_ai)
    
    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_system_initialization_with_api_key(self):
        """Test system initialization with API key."""
        with patch('openai.OpenAI'), patch('pandasai.llm.OpenAI'):
            system = ContextualNL2SQLSystem()
            
            self.assertIsInstance(system.dag, QueryDAG)
            self.assertIsNotNone(system.rewriter)
            self.assertIsNotNone(system.pandas_ai)
    
    def test_load_sample_data(self):
        """Test sample data loading."""
        system = ContextualNL2SQLSystem()
        data = system.load_sample_data()
        
        self.assertIsInstance(data, pd.DataFrame)
        self.assertGreater(len(data), 0)
        self.assertIn('customer_id', data.columns)
        self.assertIn('revenue', data.columns)
        self.assertIn('region', data.columns)
    
    def test_process_query_basic(self):
        """Test basic query processing."""
        system = ContextualNL2SQLSystem()
        
        result = system.process_query(
            "Show top customers", 
            data=self.sample_data
        )
        
        self.assertIn("query_id", result)
        self.assertIn("original_query", result)
        self.assertIn("success", result)
        self.assertEqual(result["original_query"], "Show top customers")
    
    def test_query_history(self):
        """Test query history tracking."""
        system = ContextualNL2SQLSystem()
        
        # Process multiple queries
        system.process_query("Query 1", data=self.sample_data)
        system.process_query("Query 2")
        
        history = system.get_query_history()
        
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]["original_query"], "Query 1")
        self.assertEqual(history[1]["original_query"], "Query 2")


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system."""
    
    def test_dag_integration(self):
        """Test DAG integration with query processing."""
        system = ContextualNL2SQLSystem()
        data = system.load_sample_data()
        
        # Process a sequence of related queries
        result1 = system.process_query("Show top customers", data=data)
        result2 = system.process_query(
            "Break that by region", 
            parent_query_id=result1["query_id"]
        )
        
        # Verify DAG structure
        self.assertEqual(len(system.dag), 2)
        
        # Verify parent-child relationship
        children = system.dag.get_children(result1["query_id"])
        self.assertEqual(len(children), 1)
        self.assertEqual(children[0].id, result2["query_id"])


def run_tests():
    """Run all tests."""
    print("🧪 Running Contextual NL2SQL System Tests")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestQueryNode,
        TestQueryDAG,
        TestQueryRewriter,
        TestPandasAIRunner,
        TestContextualNL2SQLSystem,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n📊 Test Results:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n{'✅ All tests passed!' if success else '❌ Some tests failed!'}")
    
    return success


if __name__ == "__main__":
    run_tests()
