"""
Query rewriter module for contextual NL2SQL query enhancement.

This module uses OpenAI's GPT-4 to rewrite ambiguous or incomplete queries
by incorporating context from upstream queries in the DAG.
"""

import os
from typing import List, Optional, Dict, Any
import openai
from dag import QueryNode, QueryDAG


class QueryRewriter:
    """
    Handles contextual query rewriting using OpenAI's GPT-4 model.
    
    This class takes incomplete or ambiguous queries and rewrites them
    into complete, standalone queries using context from previous queries.
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4"):
        """
        Initialize the QueryRewriter.
        
        Args:
            api_key: OpenAI API key (if None, will try to get from environment)
            model: OpenAI model to use for rewriting
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key must be provided or set in OPENAI_API_KEY environment variable")
        
        self.model = model
        self.client = openai.OpenAI(api_key=self.api_key)
    
    def rewrite_query(self, current_query: str, dag: QueryDAG, 
                     current_node_id: Optional[str] = None) -> str:
        """
        Rewrite a query using context from the DAG.
        
        Args:
            current_query: The query to rewrite
            dag: The QueryDAG containing context
            current_node_id: ID of the current node (if None, uses current node from DAG)
            
        Returns:
            The rewritten query as a complete, standalone query
        """
        if not current_node_id:
            current_node_id = dag.current_node_id
        
        if not current_node_id:
            # No context available, return original query
            return current_query
        
        # Get upstream context
        context_nodes = dag.get_upstream_context(current_node_id, max_depth=2)
        
        if not context_nodes:
            # No context available, return original query
            return current_query
        
        # Build context prompt
        context_prompt = self._build_context_prompt(current_query, context_nodes)
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": context_prompt
                    }
                ],
                temperature=0.1,
                max_tokens=500
            )
            
            rewritten_query = response.choices[0].message.content.strip()
            return rewritten_query
            
        except Exception as e:
            print(f"Error rewriting query: {e}")
            return current_query
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for query rewriting."""
        return """You are an expert at rewriting natural language queries for data analysis.

Your task is to take an incomplete or ambiguous query and rewrite it into a complete, standalone query using the provided context from previous queries.

Guidelines:
1. Make the query complete and unambiguous
2. Preserve the user's intent
3. Include relevant context from previous queries when needed
4. Use clear, natural language
5. Don't add unnecessary complexity
6. If the query is already complete, return it as-is

Focus on making queries that would be clear to someone who hasn't seen the previous conversation."""
    
    def _build_context_prompt(self, current_query: str, context_nodes: List[QueryNode]) -> str:
        """
        Build the context prompt for the LLM.
        
        Args:
            current_query: The query to rewrite
            context_nodes: List of context nodes from the DAG
            
        Returns:
            Formatted prompt string
        """
        prompt_parts = ["Here is the context from previous queries:\n"]
        
        for i, node in enumerate(context_nodes, 1):
            prompt_parts.append(f"Query {i}:")
            prompt_parts.append(f"  Original: {node.original_query}")
            
            if node.rewritten_query:
                prompt_parts.append(f"  Rewritten: {node.rewritten_query}")
            
            if node.sql_query:
                prompt_parts.append(f"  SQL: {node.sql_query}")
            
            if node.result_summary:
                prompt_parts.append(f"  Result: {node.result_summary}")
            
            if node.filters:
                prompt_parts.append(f"  Filters: {', '.join(node.filters)}")
            
            if node.groupings:
                prompt_parts.append(f"  Groupings: {', '.join(node.groupings)}")
            
            prompt_parts.append("")  # Empty line for readability
        
        prompt_parts.extend([
            f"Current query to rewrite: {current_query}",
            "",
            "Please rewrite the current query to be complete and standalone, incorporating relevant context from the previous queries. Return only the rewritten query without any explanation."
        ])
        
        return "\n".join(prompt_parts)
    
    def analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """
        Analyze the intent and components of a query.
        
        Args:
            query: The query to analyze
            
        Returns:
            Dictionary containing query analysis
        """
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": """Analyze the given query and extract its components. Return a JSON object with:
- intent: The main intent (filter, aggregate, drill_down, compare, etc.)
- entities: Key entities mentioned
- filters: Any filtering criteria
- groupings: Grouping dimensions
- aggregations: Aggregation functions
- temporal: Any time-related aspects
- requires_context: Boolean indicating if this query needs context from previous queries"""
                    },
                    {
                        "role": "user",
                        "content": f"Analyze this query: {query}"
                    }
                ],
                temperature=0.1,
                max_tokens=300
            )
            
            import json
            analysis = json.loads(response.choices[0].message.content)
            return analysis
            
        except Exception as e:
            print(f"Error analyzing query intent: {e}")
            return {
                "intent": "unknown",
                "entities": [],
                "filters": [],
                "groupings": [],
                "aggregations": [],
                "temporal": None,
                "requires_context": True  # Default to requiring context for safety
            }
    
    def should_use_pandas_ai(self, query: str, sql_result_available: bool = True) -> bool:
        """
        Determine if a query should be handled by Pandas AI instead of SQL.
        
        Args:
            query: The query to evaluate
            sql_result_available: Whether SQL results are available
            
        Returns:
            True if Pandas AI should be used, False otherwise
        """
        # Keywords that suggest Pandas AI would be better
        pandas_ai_keywords = [
            "trend", "pattern", "anomaly", "correlation", "insight",
            "visualize", "plot", "chart", "graph", "analyze",
            "explain", "why", "how", "what if", "predict",
            "outlier", "distribution", "summary", "describe"
        ]
        
        query_lower = query.lower()
        
        # Check for Pandas AI keywords
        has_pandas_keywords = any(keyword in query_lower for keyword in pandas_ai_keywords)
        
        # Check if it's a complex analytical question
        analytical_phrases = [
            "break down", "drill down", "deep dive", "root cause",
            "what's driving", "what caused", "compare trends"
        ]
        
        has_analytical_phrases = any(phrase in query_lower for phrase in analytical_phrases)
        
        # Use Pandas AI if:
        # 1. Query has analytical keywords/phrases
        # 2. SQL results are available for analysis
        # 3. Query seems to require reasoning beyond simple SQL
        
        return (has_pandas_keywords or has_analytical_phrases) and sql_result_available
