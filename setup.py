"""
Setup script for the Contextual NL2SQL System.

This script helps with installation and environment setup.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """Check if Python version is 3.10 or higher."""
    if sys.version_info < (3, 10):
        print("❌ Python 3.10 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True


def check_pip():
    """Check if pip is available."""
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip is available")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        return False


def install_requirements():
    """Install required packages."""
    print("\n📦 Installing requirements...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False


def setup_environment():
    """Set up environment variables."""
    print("\n🔧 Setting up environment...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("📝 Please edit .env file with your OpenAI API key")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️  No .env.example file found")
    
    # Check for OpenAI API key
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        print("✅ OPENAI_API_KEY is set")
    else:
        print("⚠️  OPENAI_API_KEY not set in environment")
        print("   Set it with: export OPENAI_API_KEY='your-key-here'")
        print("   Or add it to the .env file")


def check_docker():
    """Check if Docker is available."""
    try:
        subprocess.run(["docker", "--version"], 
                      check=True, capture_output=True)
        print("✅ Docker is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Docker not found (optional)")
        return False


def run_tests():
    """Run the test suite."""
    print("\n🧪 Running tests...")
    try:
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("❌ Some tests failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def run_demo():
    """Run the demo."""
    print("\n🚀 Running demo...")
    try:
        subprocess.run([sys.executable, "main.py"], check=True)
        print("✅ Demo completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Demo failed: {e}")
        return False


def main():
    """Main setup function."""
    print("🔧 Contextual NL2SQL System Setup")
    print("=" * 40)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # Install dependencies
    if not install_requirements():
        print("\n❌ Setup failed during package installation")
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Check optional tools
    check_docker()
    
    # Run tests
    print("\n" + "=" * 40)
    choice = input("Run tests? (y/n): ").lower().strip()
    if choice in ['y', 'yes']:
        if not run_tests():
            print("⚠️  Tests failed, but setup can continue")
    
    # Run demo
    print("\n" + "=" * 40)
    choice = input("Run demo? (y/n): ").lower().strip()
    if choice in ['y', 'yes']:
        if not run_demo():
            print("⚠️  Demo failed, check your API key configuration")
    
    print("\n✅ Setup completed!")
    print("\n📚 Next steps:")
    print("1. Set your OpenAI API key in .env file or environment")
    print("2. Run: python main.py")
    print("3. Try: python example_usage.py")
    print("4. Test: python test_system.py")
    print("5. Docker: docker-compose up")


if __name__ == "__main__":
    main()
