"""
Result Cache Module for Smart Data Reuse

This module implements intelligent caching and data reuse to avoid unnecessary
SQL queries when answers can be derived from previously fetched data.
"""

import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import json
import hashlib


@dataclass
class CachedResult:
    """
    Represents a cached query result with metadata.
    """
    query_id: str
    original_query: str
    data: pd.DataFrame
    timestamp: datetime
    metadata: Dict[str, Any]
    
    def can_derive(self, new_query: str, new_metadata: Dict[str, Any]) -> bool:
        """
        Check if a new query can be derived from this cached result.
        
        Args:
            new_query: The new query to check
            new_metadata: Metadata about the new query
            
        Returns:
            True if new query can be derived from this cached data
        """
        # Check if we have enough data to answer the new query
        return self._can_subset(new_query, new_metadata) or \
               self._can_aggregate(new_query, new_metadata) or \
               self._can_filter(new_query, new_metadata)
    
    def _can_subset(self, new_query: str, new_metadata: Dict[str, Any]) -> bool:
        """Check if new query is a subset of cached data."""
        new_query_lower = new_query.lower()
        original_lower = self.original_query.lower()
        
        # Pattern: "top N" where N < original N
        if "top" in new_query_lower and "top" in original_lower:
            try:
                # Extract numbers from queries
                new_num = self._extract_number(new_query)
                original_num = self._extract_number(self.original_query)
                
                if new_num and original_num and new_num <= original_num:
                    # Check if same entity type (customers, suppliers, etc.)
                    if self._same_entity_type(new_query, self.original_query):
                        return True
            except:
                pass
        
        return False
    
    def _can_aggregate(self, new_query: str, new_metadata: Dict[str, Any]) -> bool:
        """Check if new query is an aggregation of cached data."""
        new_query_lower = new_query.lower()
        
        # Aggregation keywords
        agg_keywords = ["sum", "average", "count", "total", "mean", "max", "min"]
        
        if any(keyword in new_query_lower for keyword in agg_keywords):
            # Check if we have the necessary columns
            required_columns = new_metadata.get("required_columns", [])
            available_columns = set(self.data.columns.str.lower())
            
            if all(col.lower() in available_columns for col in required_columns):
                return True
        
        return False
    
    def _can_filter(self, new_query: str, new_metadata: Dict[str, Any]) -> bool:
        """Check if new query is a filter of cached data."""
        new_query_lower = new_query.lower()
        
        # Filter keywords
        filter_keywords = ["where", "filter", "only", "with", "having"]
        
        if any(keyword in new_query_lower for keyword in filter_keywords):
            # Check if we have the necessary columns for filtering
            filter_columns = new_metadata.get("filter_columns", [])
            available_columns = set(self.data.columns.str.lower())
            
            if all(col.lower() in available_columns for col in filter_columns):
                return True
        
        return False
    
    def _extract_number(self, query: str) -> Optional[int]:
        """Extract number from query like 'top 50' or 'first 10'."""
        import re
        numbers = re.findall(r'\b(\d+)\b', query)
        return int(numbers[0]) if numbers else None
    
    def _same_entity_type(self, query1: str, query2: str) -> bool:
        """Check if two queries refer to the same entity type."""
        entities = ["customer", "supplier", "product", "order", "sale", "employee"]
        
        query1_entities = [e for e in entities if e in query1.lower()]
        query2_entities = [e for e in entities if e in query2.lower()]
        
        return len(set(query1_entities) & set(query2_entities)) > 0


class SmartResultCache:
    """
    Intelligent result cache that can derive answers from previously fetched data.
    """
    
    def __init__(self, max_cache_size: int = 100):
        """
        Initialize the smart cache.
        
        Args:
            max_cache_size: Maximum number of results to cache
        """
        self.cache: Dict[str, CachedResult] = {}
        self.max_cache_size = max_cache_size
        self.access_order: List[str] = []  # For LRU eviction
    
    def add_result(self, query_id: str, original_query: str, 
                   data: pd.DataFrame, metadata: Dict[str, Any] = None):
        """
        Add a query result to the cache.
        
        Args:
            query_id: Unique identifier for the query
            original_query: The original query text
            data: The result DataFrame
            metadata: Additional metadata about the query
        """
        if metadata is None:
            metadata = {}
        
        cached_result = CachedResult(
            query_id=query_id,
            original_query=original_query,
            data=data.copy(),
            timestamp=datetime.now(),
            metadata=metadata
        )
        
        self.cache[query_id] = cached_result
        
        # Update access order
        if query_id in self.access_order:
            self.access_order.remove(query_id)
        self.access_order.append(query_id)
        
        # Evict old entries if cache is full
        self._evict_if_needed()
    
    def can_derive_from_cache(self, new_query: str, 
                             new_metadata: Dict[str, Any] = None) -> Optional[Tuple[str, CachedResult]]:
        """
        Check if a new query can be derived from cached results.
        
        Args:
            new_query: The new query to check
            new_metadata: Metadata about the new query
            
        Returns:
            Tuple of (cache_key, cached_result) if derivable, None otherwise
        """
        if new_metadata is None:
            new_metadata = {}
        
        # Check each cached result in reverse order (most recent first)
        for query_id in reversed(self.access_order):
            if query_id in self.cache:
                cached_result = self.cache[query_id]
                if cached_result.can_derive(new_query, new_metadata):
                    return query_id, cached_result
        
        return None
    
    def derive_result(self, cache_key: str, cached_result: CachedResult, 
                     new_query: str, new_metadata: Dict[str, Any] = None) -> pd.DataFrame:
        """
        Derive a new result from cached data.
        
        Args:
            cache_key: The cache key of the source result
            cached_result: The cached result to derive from
            new_query: The new query
            new_metadata: Metadata about the new query
            
        Returns:
            Derived DataFrame result
        """
        if new_metadata is None:
            new_metadata = {}
        
        # Update access order
        if cache_key in self.access_order:
            self.access_order.remove(cache_key)
        self.access_order.append(cache_key)
        
        # Derive based on query type
        if self._is_subset_query(new_query, cached_result.original_query):
            return self._derive_subset(cached_result.data, new_query)
        elif self._is_aggregation_query(new_query):
            return self._derive_aggregation(cached_result.data, new_query, new_metadata)
        elif self._is_filter_query(new_query):
            return self._derive_filter(cached_result.data, new_query, new_metadata)
        else:
            # Fallback: return original data
            return cached_result.data.copy()
    
    def _is_subset_query(self, new_query: str, original_query: str) -> bool:
        """Check if new query is asking for a subset."""
        new_query_lower = new_query.lower()
        original_lower = original_query.lower()
        
        return ("top" in new_query_lower and "top" in original_lower) or \
               ("first" in new_query_lower) or \
               ("limit" in new_query_lower)
    
    def _is_aggregation_query(self, query: str) -> bool:
        """Check if query is asking for aggregation."""
        query_lower = query.lower()
        agg_keywords = ["sum", "average", "count", "total", "mean", "max", "min", "avg"]
        return any(keyword in query_lower for keyword in agg_keywords)
    
    def _is_filter_query(self, query: str) -> bool:
        """Check if query is asking for filtering."""
        query_lower = query.lower()
        filter_keywords = ["where", "filter", "only", "with", "having", "exclude"]
        return any(keyword in query_lower for keyword in filter_keywords)
    
    def _derive_subset(self, data: pd.DataFrame, new_query: str) -> pd.DataFrame:
        """Derive a subset from cached data."""
        # Extract number from query
        import re
        numbers = re.findall(r'\b(\d+)\b', new_query)
        
        if numbers:
            limit = int(numbers[0])
            return data.head(limit).copy()
        
        return data.copy()
    
    def _derive_aggregation(self, data: pd.DataFrame, new_query: str, 
                           metadata: Dict[str, Any]) -> pd.DataFrame:
        """Derive aggregation from cached data."""
        query_lower = new_query.lower()
        
        # Simple aggregations
        if "count" in query_lower:
            return pd.DataFrame({"count": [len(data)]})
        elif "sum" in query_lower and "revenue" in query_lower:
            if "revenue" in data.columns:
                return pd.DataFrame({"total_revenue": [data["revenue"].sum()]})
        elif "average" in query_lower or "avg" in query_lower:
            numeric_cols = data.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                return pd.DataFrame({f"avg_{col}": [data[col].mean()] for col in numeric_cols})
        
        # Fallback: return summary statistics
        return data.describe()
    
    def _derive_filter(self, data: pd.DataFrame, new_query: str, 
                      metadata: Dict[str, Any]) -> pd.DataFrame:
        """Derive filtered data from cached data."""
        # This is a simplified implementation
        # In practice, you'd parse the filter conditions more sophisticatedly
        
        query_lower = new_query.lower()
        filtered_data = data.copy()
        
        # Example filters
        if "high" in query_lower and "revenue" in query_lower:
            if "revenue" in data.columns:
                threshold = data["revenue"].quantile(0.8)  # Top 20%
                filtered_data = data[data["revenue"] >= threshold]
        elif "recent" in query_lower and "date" in data.columns:
            # Filter for recent dates
            if "date" in data.columns:
                recent_date = data["date"].max() - pd.Timedelta(days=30)
                filtered_data = data[data["date"] >= recent_date]
        
        return filtered_data
    
    def _evict_if_needed(self):
        """Evict oldest entries if cache exceeds max size."""
        while len(self.cache) > self.max_cache_size:
            oldest_key = self.access_order.pop(0)
            if oldest_key in self.cache:
                del self.cache[oldest_key]
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cache_size": len(self.cache),
            "max_size": self.max_cache_size,
            "cached_queries": [
                {
                    "query_id": result.query_id,
                    "query": result.original_query,
                    "data_shape": result.data.shape,
                    "timestamp": result.timestamp.isoformat()
                }
                for result in self.cache.values()
            ]
        }
    
    def clear_cache(self):
        """Clear all cached results."""
        self.cache.clear()
        self.access_order.clear()
    
    def remove_expired(self, max_age_hours: int = 24):
        """Remove cached results older than specified hours."""
        cutoff_time = datetime.now() - pd.Timedelta(hours=max_age_hours)
        
        expired_keys = [
            key for key, result in self.cache.items()
            if result.timestamp < cutoff_time
        ]
        
        for key in expired_keys:
            if key in self.cache:
                del self.cache[key]
            if key in self.access_order:
                self.access_order.remove(key)
